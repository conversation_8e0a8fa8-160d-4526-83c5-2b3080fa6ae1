using System;
using System.IO.Ports;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using Driver.Core.Models;
using Furion.Logging;
using HslCommunication.BasicFramework;

namespace TechMationAk;

/// <summary>
/// </summary>
public class ClientHandler : IDisposable
{
    private readonly TechMationAkData _hxData;
    private readonly StringBuilder _receivedData = new();
    private readonly DriverInfoDto _driverInfo;
    private readonly CancellationTokenSource _tokenSource = new();
    private string _ip;

    /// <summary>
    ///     连接状态
    /// </summary>
    public bool IsConnected
    {
        get
        {
            try
            {
                if (_tcpClient == null && _serialPort == null)
                    return false;
                if (_tcpClient == null) return true;
                return _tcpClient.Connected;
            }
            catch
            {
                return false;
            }
        }
    }

    public ClientHandler(TechMationAkData hxData, DriverInfoDto driverInfo)
    {
        _hxData = hxData;
        _driverInfo = driverInfo;
    }

    #region 网口

    private TcpClient _tcpClient;

    public void Connect(string ipAddress, int port)
    {
        _ip = ipAddress + ":" + port;
        _tcpClient = new TcpClient();
        _tcpClient.Connect(ipAddress, port);
        _ = _driverInfo.Socket.Send("Connected to server: " + ipAddress + ":" + port, _driverInfo.DeviceId + "_Logs");
        _receivedData.Clear();
        var receiveThread = new Thread(ReceiveData);
        receiveThread.Start();
        Thread threadB = new Thread(WaitAndProcessData);
        threadB.Start();
    }

    private void ReceiveData()
    {
        var stream = _tcpClient.GetStream();
        while (!_tokenSource.IsCancellationRequested)
        {
            try
            {
                var buffer = new byte[10240];
                var bytesRead = stream.Read(buffer, 0, buffer.Length);
                if (bytesRead > 0)
                {
                    lock (_lock)
                    {
                        var resizedBuffer = new byte[bytesRead];
                        Array.Copy(buffer, resizedBuffer, resizedBuffer.Length);
                        buffer = resizedBuffer;
                        var data = SoftBasic.ByteToHexString(buffer, ' ');
                        _ = _driverInfo.Socket.Send($"来源:[{_ip}]\t 时间：{DateTime.Now}\t【收】:" + data, _driverInfo.DeviceId + "_Logs");
                        if (_receivedData.Length > 0)
                            _receivedData.Append(' ');
                        _receivedData.Append(data);
                        _dataAvailable = true;
                        Monitor.Pulse(_lock); // 通知等待的线程
                    }
                }
            }
            catch (Exception ex)
            {
                if (!_tcpClient.Connected)
                {
                    _ = _driverInfo.Socket.Send("连接已断开", _driverInfo.DeviceId + "_Logs");
                    Log.Information("连接已经断开");
                    break;
                }
                _ = _driverInfo.Socket.Send("【收】 接收数据发生错误: " + ex.Message, _driverInfo.DeviceId + "_Logs");
            }

            Thread.Sleep(20);
        }
    }

    #endregion 网口

    #region 串口

    private SerialPort _serialPort;

    public void Connect(string serialNumber)
    {
        _serialPort = new SerialPort();
        _serialPort.PortName = serialNumber;
        _serialPort.BaudRate = 38400;
        _serialPort.DataBits = 8;
        _serialPort.StopBits = StopBits.One;
        _serialPort.Parity = Parity.None;
        _serialPort.Open();
        _ = _driverInfo.Socket.Send("Connected to serial port: " + _serialPort.PortName, _driverInfo.DeviceId + "_Logs");
        var receiveThread = new Thread(SerialReceiveData);
        receiveThread.Start();
        Thread threadB = new Thread(WaitAndProcessData);
        threadB.Start();
    }

    /// <summary>
    ///  锁
    /// </summary>
    private static object _lock = new();
    /// <summary>
    /// 标记是否收到新的报文
    /// </summary>
    private static bool _dataAvailable;
    private void SerialReceiveData()
    {
        var stream = _serialPort;
        _ = _driverInfo.Socket.Send($"来源:[{stream.PortName}]\t 时间：{DateTime.Now}\t 接收数据中·······", _driverInfo.DeviceId + "_Logs");
        while (!_tokenSource.IsCancellationRequested)
        {
            try
            {
                var buffer = new byte[2048];
                var bytesRead = stream.Read(buffer, 0, buffer.Length);
                if (bytesRead > 0)
                {
                    lock (_lock)
                    {
                        var resizedBuffer = new byte[bytesRead];
                        Array.Copy(buffer, resizedBuffer, resizedBuffer.Length);
                        buffer = resizedBuffer;
                        var data = SoftBasic.ByteToHexString(buffer, ' ');
                        _ = _driverInfo.Socket.Send($"来源:[{_serialPort?.PortName}]\t 时间：{DateTime.Now}\t【收】: " + data, _driverInfo.DeviceId + "_Logs");
                        if (_receivedData.Length > 0)
                            _receivedData.Append(' ');
                        _receivedData.Append(data);
                        _dataAvailable = true;
                        Monitor.Pulse(_lock); // 通知等待的线程
                    }
                }
            }
            catch (Exception ex)
            {
                if (!stream.IsOpen)
                {
                    _ = _driverInfo.Socket.Send("连接已断开", _driverInfo.DeviceId + "_Logs");
                    break;
                }
                _ = _driverInfo.Socket.Send("【收】 接收数据发生错误:" + ex.Message, _driverInfo.DeviceId + "_Logs");
            }

            Thread.Sleep(20);
        }
    }

    #endregion 串口
    void WaitAndProcessData()
    {
        lock (_lock)
        {
            while (true)
            {
                try
                {
                    if (!_dataAvailable)
                    {
                        Monitor.Wait(_lock); // 等待通知
                        continue;
                    }

                    string copiedData = GetData();
                    ClearData();
                    _dataAvailable = false;
                    ProcessData(copiedData); // 调用处理数据的方法
                }
                catch
                {
                    ClearData();
                }
            }
        }
    }
    string GetData()
    {
        lock (_lock)
        {
            return _receivedData.ToString();
        }
    }
    
    void ClearData()
    {
        lock (_lock)
        {
            _receivedData.Clear();
        }
    }
    
    /// <summary>
    /// </summary>
    private void ProcessData(string data)
    {
        var startIndex = -1;
        try
        {
            // 使用正则表达式匹配符合条件的报文头
            var match = Regex.Match(data, "02");
            if (!match.Success)
                return;
            // 获取符合条件的报文头的索引位置
            startIndex = match.Index;
            // 移除不符合条件的报文
            data = data.Remove(0, startIndex);
            // 检查移除后的数据是否以符合条件的报文起始
            if (!data.StartsWith("02"))
                return;
        }
        catch (Exception e)
        {
            _ = _driverInfo.Socket.Send("【TechMationAk】: 移除报文 error:" + e.Message, _driverInfo.DeviceId + "_Logs");
        }
        var bytes = ConvertDataToBytes(data);
        while (startIndex != -1 && !_tokenSource.IsCancellationRequested)
        {
            if (bytes.Length <= 7)
                break;
            int dec1 = ByteToDec(bytes, startIndex +1,startIndex +2); 
            // 长度*2 + 帧头1 + 帧尾1 + 用途报文2*2
            var endIndex = dec1 * 2 + 2 + 4 - 1;
            if (endIndex > bytes.Length)
            {
                _ = _driverInfo.Socket.Send($"【收】 检测到报文不完整,等待新的数据送达,endIndex：{endIndex},byteLength:{bytes.Length}", _driverInfo.DeviceId + "_Logs");
                break;
            }

            // 帧尾是不是0x03
            if (bytes[endIndex] == 0x03)
            {
                var processedMessage = new byte[endIndex + 1];
                Array.Copy(bytes, 0, processedMessage, 0, processedMessage.Length);
                try
                {
                    switch (bytes[3])
                    {
                        case 0x43 when bytes[4] == 0x41:
                            DealData43_41(processedMessage, processedMessage.Length); // 测试通过
                            break;
                        case 0x43 when bytes[4] == 0x44:
                            DealData43_44(processedMessage, processedMessage.Length); //测试合格
                            break;
                        case 0x43 when bytes[4] == 0x45:
                            DealData43_45(processedMessage, processedMessage.Length); //测试合格
                            break;
                        case 0x45 when bytes[4] == 0x43: // 测试合格
                        {
                            if (DealData45_43(processedMessage, processedMessage.Length) == 1)
                                _hxData.MoldOpeningTotal += 1;
                            break;
                        }
                        case 0x33 when bytes[4] == 0x32:
                            dealData33_32(processedMessage, processedMessage.Length);
                            break;
                        case 0x33 when bytes[4] == 0x33:
                            DealData33_33(processedMessage, processedMessage.Length);
                            break;
                        case 0x33 when bytes[4] == 0x36:
                            DealData33_36(processedMessage, processedMessage.Length);
                            break;
                    }
                }
                catch (Exception ex)
                {
                    _ = _driverInfo.Socket.Send("【报文解析】 Error: " + ex.Message, _driverInfo.DeviceId + "_Logs");
                    bytes = RemoveBytesFromArray(bytes, 0, bytes[endIndex] +1);
                }
            }
            
            try
            {
                // 验证是不是连续报文
                data = data.Length >= (endIndex + 1) * 3 ? data.Remove(0, (endIndex + 1) * 3) : data.Remove(0, (endIndex + 1) * 3 - 1);
                bytes = RemoveBytesFromArray(bytes, 0, endIndex + 1);
            }
            catch (Exception e)
            {
                _ = _driverInfo.Socket.Send("【TechMationAk】:移除对象失败:" + e.Message + $", 【data:{data.Length}】,endIndex:【{(endIndex + 1) * 3}】", _driverInfo.DeviceId + "_Logs");
                return;
            }
            // 寻找下一个符合条件的报文头的索引位置
            var match = Regex.Match(data, "02");
            startIndex = match.Success ? match.Index : -1;
        }
    }
    
    int ByteToDec(byte[] bytes, int index,int indexEnd)
    {
        string hex = bytes[index].ToString("X2") + bytes[indexEnd].ToString("X2");
        int dec = Convert.ToInt32(hex, 16);
        return dec;
    }
    
    byte[] ConvertDataToBytes(string data)
    {
        var parts = data.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        var bytes = new byte[parts.Length];
    
        for (var i = 0; i < parts.Length; i++)
        {
            bytes[i] = Convert.ToByte(parts[i], 16);
        }
    
        return bytes;
    }


    private byte[] RemoveBytesFromArray(byte[] source, int startIndex, int length)
    {
        var destination = new byte[source.Length - length];
        Array.Copy(source, 0, destination, 0, startIndex);
        Array.Copy(source, startIndex + length, destination, startIndex, source.Length - startIndex - length);
        return destination;
    }

    /// <summary>
    ///     处理33_36的数据，来源控制器
    ///     修改设定值，在HMI修改数据之后，会发出对应数值
    /// </summary>
    /// <param name="buff"></param>
    /// <param name="len"></param>
    private void DealData33_36(byte[] buff, int len)
    {
        if (len != 16)
            return;
        var id = GetShortValue(buff, 7);
        var value = GetShortValue(buff, 9);
        switch (id)
        {
            // 开模、关模设定值
            case 0x0080:
                _hxData.MoldClosingPressure[0] = value;
                break; // 关模一段压力
            case 0x0081:
                _hxData.MoldClosingSpeed[0] = value;
                break; // 关模一段速度
            case 0x0082:
                _hxData.MoldClosingPosition[0] = value / 10.0;
                break; // 关模一段位置
            case 0x0083:
                _hxData.MoldClosingPressure[1] = value;
                break; // 关模二段压力
            case 0x0084:
                _hxData.MoldClosingSpeed[1] = value;
                break; // 关模二段速度
            case 0x0085:
                _hxData.MoldClosingPosition[1] = value / 10.0;
                break; // 关模二段位置
            case 0x0086:
                _hxData.MoldClosingPressure[2] = value;
                break; // 关模三段压力
            case 0x0087:
                _hxData.MoldClosingSpeed[2] = value;
                break; // 关模三段速度
            case 0x0088:
                _hxData.MoldClosingPosition[2] = value / 10.0;
                break; // 关模三段位置
            case 0x0089:
                _hxData.MoldClosingPressure[3] = value;
                break; // 关模低压压力
            case 0x008A:
                _hxData.MoldClosingSpeed[3] = value;
                break; // 关模低压速度
            case 0x008B:
                _hxData.MoldClosingPosition[3] = value / 10.0;
                break; // 关模低压位置
            case 0x008C:
                _hxData.MoldClosingPressure[4] = value;
                break; // 关模高压压力
            case 0x008D:
                _hxData.MoldClosingSpeed[4] = value;
                break; // 关模高压速度
            case 0x0330:
                _hxData.MoldOpeningPressure[0] = value;
                break; // 开模一段压力
            case 0x0331:
                _hxData.MoldOpeningSpeed[0] = value;
                break; // 开模一段速度
            case 0x0332:
                _hxData.MoldOpeningPosition[0] = value / 10.0;
                break; // 开模一段位置
            case 0x0333:
                _hxData.MoldOpeningPressure[1] = value;
                break; // 开模二段压力
            case 0x0334:
                _hxData.MoldOpeningSpeed[1] = value;
                break; // 开模二段速度
            case 0x0335:
                _hxData.MoldOpeningPosition[1] = value / 10.0;
                break; // 开模二段位置
            case 0x0336:
                _hxData.MoldOpeningPressure[2] = value;
                break; // 开模三段压力
            case 0x0337:
                _hxData.MoldOpeningSpeed[2] = value;
                break; // 开模三段速度
            case 0x0338:
                _hxData.MoldOpeningPosition[2] = value / 10.0;
                break; // 开模三段位置
            case 0x0339:
                _hxData.MoldOpeningPressure[3] = value;
                break; // 开模四段压力
            case 0x033A:
                _hxData.MoldOpeningSpeed[3] = value;
                break; // 开模四段速度
            case 0x033B:
                _hxData.MoldOpeningPosition[3] = value / 10.0;
                break; // 开模四段位置
            case 0x033C:
                _hxData.MoldOpeningPressure[4] = value;
                break; // 开模五段压力
            case 0x033D:
                _hxData.MoldOpeningSpeed[4] = value;
                break; // 开模五段速度
            case 0x02F4:
                _hxData.RecyclingTime = value / 100.0;
                break; // 再循环计时
            case 0x00BA: // 模具冷却计时低位数据
                _hxData.MoldCoolingTimeLow = value;
                _hxData.GetMoldCoolingTimeLow = true;
                break;
            case 0x00BB: // 模具冷却计时高位数据
                _hxData.MoldCoolingTimeHigh = value;
                if (_hxData.GetMoldCoolingTimeLow)
                {
                    _hxData.MoldCoolingTime = (_hxData.MoldCoolingTimeLow + (_hxData.MoldCoolingTimeHigh << 16)) / 100.0;
                    _hxData.GetMoldCoolingTimeLow = false;
                }

                break;

            // 射出、保压设定值
            case 0x0250:
                _hxData.HoldingPressure[0] = value;
                break; // 保压一段压力
            case 0x0251:
                _hxData.HoldingSpeed[0] = value;
                break; // 保压一段速度
            case 0x0252:
                _hxData.HoldingTime[0] = value / 100.0;
                break; // 保压一段时间
            case 0x0254:
                _hxData.HoldingPressure[1] = value;
                break; // 保压二段压力
            case 0x0255:
                _hxData.HoldingSpeed[1] = value;
                break; // 保压二段速度
            case 0x0256:
                _hxData.HoldingTime[1] = value / 100.0;
                break; // 保压二段时间
            case 0x0258:
                _hxData.HoldingPressure[2] = value;
                break; // 保压三段压力
            case 0x0259:
                _hxData.HoldingSpeed[2] = value;
                break; // 保压三段速度
            case 0x025A:
                _hxData.HoldingTime[2] = value / 100.0;
                break; // 保压三段时间
            case 0x025C:
                _hxData.HoldingPressure[3] = value;
                break; // 保压四段压力
            case 0x025D:
                _hxData.HoldingSpeed[3] = value;
                break; // 保压四段速度
            case 0x025E:
                _hxData.HoldingTime[3] = value / 100.0;
                break; // 保压四段时间
            case 0x0260:
                _hxData.HoldingPressure[4] = value;
                break; // 保压五段压力
            case 0x0261:
                _hxData.HoldingSpeed[4] = value;
                break; // 保压五段速度
            case 0x0262:
                _hxData.HoldingTime[4] = value / 100.0;
                break; // 保压五段时间
            case 0x0264:
                _hxData.HoldingPressure[5] = value;
                break; // 保压六段压力
            case 0x0265:
                _hxData.HoldingSpeed[5] = value;
                break; // 保压六段速度
            case 0x0266:
                _hxData.HoldingTime[5] = value / 100.0;
                break; // 保压六段时间
            case 0x0290:
                _hxData.InjectionPressure[0] = value;
                break; // 射出一段压力
            case 0x0291:
                _hxData.InjectionSpeed[0] = value;
                break; // 射出一段速度
            case 0x0294:
                _hxData.InjectionPosition[1] = value / 10.0;
                break; // 射出二段位置
            case 0x0296:
                _hxData.InjectionPressure[1] = value;
                break; // 射出二段压力
            case 0x0297:
                _hxData.InjectionSpeed[1] = value;
                break; // 射出二段速度
            case 0x029A:
                _hxData.InjectionPosition[2] = value / 10.0;
                break; // 射出三段位置
            case 0x029C:
                _hxData.InjectionPressure[2] = value;
                break; // 射出三段压力
            case 0x029D:
                _hxData.InjectionSpeed[2] = value;
                break; // 射出三段速度
            case 0x02A0:
                _hxData.InjectionPosition[3] = value / 10.0;
                break; // 射出四段位置
            case 0x02A2:
                _hxData.InjectionPressure[3] = value;
                break; // 射出四段压力
            case 0x02A3:
                _hxData.InjectionSpeed[3] = value;
                break; // 射出四段速度
            case 0x02A6:
                _hxData.InjectionPosition[4] = value / 10.0;
                break; // 射出五段位置
            case 0x02A8:
                _hxData.InjectionPressure[4] = value;
                break; // 射出五段压力
            case 0x02A9:
                _hxData.InjectionSpeed[4] = value;
                break; // 射出五段速度
            case 0x02AC:
                _hxData.InjectionPosition[5] = value / 10.0;
                break; // 射出六段位置
            case 0x02AE:
                _hxData.InjectionPressure[5] = value;
                break; // 射出六段压力
            case 0x02AF:
                _hxData.InjectionSpeed[5] = value;
                break; // 射出六段速度
            case 0x0278:
                _hxData.SwitchHoldingPressure = value;
                break; // 转保压压力
            case 0x0279:
                _hxData.SwitchHoldingPosition = value / 10.0;
                break; // 转保压位置
            case 0x027A:
                _hxData.SwitchHoldingSelection = value;
                break; // 转保压选择
            case 0x0292:
                _hxData.SwitchHoldingTime = value / 100.0;
                break; // 转保压时间

            // 储料、射退设定值
            case 0x0050:
                _hxData.StoragePressure[0] = value;
                break; // 储料一段压力
            case 0x0051:
                _hxData.StorageBackPressure[0] = value;
                break; // 储料一段背压
            case 0x0052:
                _hxData.StorageSpeed[0] = value;
                break; // 储料一段速度
            case 0x0053:
                _hxData.StoragePosition[0] = value / 10.0;
                break; // 储料一段位置
            case 0x0054:
                _hxData.StoragePressure[1] = value;
                break; // 储料二段压力
            case 0x0055:
                _hxData.StorageBackPressure[1] = value;
                break; // 储料二段背压
            case 0x0056:
                _hxData.StorageSpeed[1] = value;
                break; // 储料二段速度
            case 0x0057:
                _hxData.StoragePosition[1] = value / 10.0;
                break; // 储料二段位置
            case 0x0058:
                _hxData.StoragePressure[2] = value;
                break; // 储料三段压力
            case 0x0059:
                _hxData.StorageBackPressure[2] = value;
                break; // 储料三段背压
            case 0x005A:
                _hxData.StorageSpeed[2] = value;
                break; // 储料三段速度
            case 0x005B:
                _hxData.StoragePosition[2] = value / 10.0;
                break; // 储料三段位置
            case 0x005C:
                _hxData.StoragePressure[3] = value;
                break; // 储料四段压力
            case 0x005D:
                _hxData.StorageBackPressure[3] = value;
                break; // 储料四段背压
            case 0x005E:
                _hxData.StorageSpeed[3] = value;
                break; // 储料四段速度
            case 0x005F:
                _hxData.StoragePosition[3] = value / 10.0;
                break; // 储料四段位置
            case 0x0060:
                _hxData.StoragePressure[4] = value;
                break; // 储料五段压力
            case 0x0061:
                _hxData.StorageBackPressure[4] = value;
                break; // 储料五段背压
            case 0x0062:
                _hxData.StorageSpeed[4] = value;
                break; // 储料五段速度
            case 0x0390:
                _hxData.InjectionBackPressure = value;
                break; // 射退压力
            case 0x0391:
                _hxData.InjectionBackSpeed = value;
                break; // 射退速度
            case 0x0393:
                _hxData.InjectionBackDistance = value / 10.0;
                break; // 射退距离
            case 0x0395:
                _hxData.InjectionBackMode = value;
                break; // 射退模式，0表示储料后，1表示冷却后
            case 0x0397:
                _hxData.InjectionBackDistanceBeforeStorage = value / 10.0;
                break; // 储前射退距离
            case 0x00B8:
                _hxData.CoolingBeforeStorage = value / 100.0;
                break; // 储前冷却

            // 托模进、托模退设定
            case 0x01A0:
                _hxData.EjectorForwardPressure[0] = value;
                break; // 托模进一段压力
            case 0x01A1:
                _hxData.EjectorForwardSpeed[0] = value;
                break; // 托模进一段速度
            case 0x01A2:
                _hxData.EjectorForwardPosition[0] = value / 10.0;
                break; // 托模进一段位置
            case 0x01A3:
                _hxData.EjectorForwardPressure[1] = value;
                break; // 托模进二段压力
            case 0x01A4:
                _hxData.EjectorForwardSpeed[1] = value;
                break; // 托模进二段速度
            case 0x01A5:
                _hxData.EjectorForwardPosition[1] = value / 10.0;
                break; // 托模进二段位置
            case 0x01A6:
                _hxData.EjectorBackwardPressure[0] = value;
                break; // 托模退一段压力
            case 0x01A7:
                _hxData.EjectorBackwardSpeed[0] = value;
                break; // 托模退一段速度
            case 0x01A8:
                _hxData.EjectorBackwardPosition[0] = value / 10.0;
                break; // 托模退一段位置
            case 0x01A9:
                _hxData.EjectorBackwardPressure[1] = value;
                break; // 托模退二段压力
            case 0x01AA:
                _hxData.EjectorBackwardSpeed[1] = value;
                break; // 托模退二段速度
            case 0x01AB:
                _hxData.EjectorBackwardPosition[1] = value / 10.0;
                break; // 托模退二段位置
            case 0x01AE:
                _hxData.EjectorForwardDelayTime = value / 100.0;
                break; // 托模进延迟时间
            case 0x01AF:
                _hxData.EjectorBackwardDelayTime = value / 100.0;
                break; // 托模退延迟时间

            // 中子设定
            case 0x00C5:
                _hxData.CoreAForwardPressure = value;
                break; // 中子A进压力
            case 0x00C6:
                _hxData.CoreAForwardSpeed = value;
                break; // 中子A进速度
            case 0x00C7:
                _hxData.CoreAForwardTime = value / 100.0;
                break; // 中子A进动作时间
            case 0x00C8:
                _hxData.CoreAForwardCount = value;
                break; // 中子A进绞牙计数
            case 0x00CF:
                _hxData.CoreAForwardPosition = value / 10.0;
                break; // 中子A进动作位置
            case 0x00D4:
                _hxData.CoreABackwardPressure = value;
                break; // 中子A退压力
            case 0x00D5:
                _hxData.CoreABackwardSpeed = value;
                break; // 中子A退速度
            case 0x00D6:
                _hxData.CoreABackwardTime = value / 100.0;
                break; // 中子A退动作时间
            case 0x00D7:
                _hxData.CoreABackwardCount = value;
                break; // 中子A退绞牙计数
            case 0x00DD:
                _hxData.CoreABackward2 = value;
                break; // 中子A退绞牙退二
            case 0x00DE:
                _hxData.CoreABackwardPosition = value / 10.0;
                break; // 中子A退动作位置
            case 0x00E5:
                _hxData.CoreBForwardPressure = value;
                break; // 中子B进压力
            case 0x00E6:
                _hxData.CoreBForwardSpeed = value;
                break; // 中子B进速度
            case 0x00E7:
                _hxData.CoreBForwardTime = value / 100.0;
                break; // 中子B进动作时间
            case 0x00E8:
                _hxData.CoreBForwardCount = value;
                break; // 中子B进绞牙计数
            case 0x00EF:
                _hxData.CoreBForwardPosition = value / 10.0;
                break; // 中子B进动作位置
            case 0x00F4:
                _hxData.CoreBBackwardPressure = value;
                break; // 中子B退压力
            case 0x00F5:
                _hxData.CoreBBackwardSpeed = value;
                break; // 中子B退速度
            case 0x00F6:
                _hxData.CoreBBackwardTime = value / 100.0;
                break; // 中子B退动作时间
            case 0x00F7:
                _hxData.CoreBBackwardCount = value;
                break; // 中子B退绞牙计数
            case 0x00FE:
                _hxData.CoreBBackwardPosition = value / 10.0;
                break; // 中子B退动作位置
            case 0x0105:
                _hxData.CoreCForwardPressure = value;
                break; // 中子C进压力
            case 0x0106:
                _hxData.CoreCForwardSpeed = value;
                break; // 中子C进速度
            case 0x0107:
                _hxData.CoreCForwardTime = value / 100.0;
                break; // 中子C进动作时间
            case 0x0108:
                _hxData.CoreCForwardCount = value;
                break; // 中子C进绞牙计数
            case 0x010F:
                _hxData.CoreCForwardPosition = value / 10.0;
                break; // 中子C进动作位置
            case 0x0114:
                _hxData.CoreCBackwardPressure = value;
                break; // 中子C退压力
            case 0x0115:
                _hxData.CoreCBackwardSpeed = value;
                break; // 中子C退速度
            case 0x0116:
                _hxData.CoreCBackwardTime = value / 100.0;
                break; // 中子C退动作时间
            case 0x0117:
                _hxData.CoreCBackwardCount = value;
                break; // 中子C退绞牙计数
            case 0x011E:
                _hxData.CoreCBackwardPosition = value / 10.0;
                break; // 中子C退动作位置
            case 0x0125:
                _hxData.CoreDForwardPressure = value;
                break; // 中子D进压力
            case 0x0126:
                _hxData.CoreDForwardSpeed = value;
                break; // 中子D进速度
            case 0x0127:
                _hxData.CoreDForwardTime = value / 100.0;
                break; // 中子D进动作时间
            case 0x0128:
                _hxData.CoreDForwardCount = value;
                break; // 中子D进绞牙计数
            case 0x012F:
                _hxData.CoreDForwardPosition = value / 10.0;
                break; // 中子D进动作位置
            case 0x0134:
                _hxData.CoreDBackwardPressure = value;
                break; // 中子D退压力
            case 0x0135:
                _hxData.CoreDBackwardSpeed = value;
                break; // 中子D退速度
            case 0x0136:
                _hxData.CoreDBackwardTime = value / 100.0;
                break; // 中子D退动作时间
            case 0x0137:
                _hxData.CoreDBackwardCount = value;
                break; // 中子D退绞牙计数
            case 0x013E:
                _hxData.CoreDBackwardPosition = value / 10.0;
                break; // 中子D退动作位置

            // 温度设定
            case 0x03A0:
                _hxData.TempSet[0] = value;
                break; // 温度一段
            case 0x03A1:
                _hxData.TempSet[1] = value;
                break; // 温度二段
            case 0x03A2:
                _hxData.TempSet[2] = value;
                break; // 温度三段
            case 0x03A3:
                _hxData.TempSet[3] = value;
                break; // 温度四段
            case 0x03A4:
                _hxData.TempSet[4] = value;
                break; // 温度五段
            case 0x03A5:
                _hxData.TempSet[5] = value;
                break; // 温度六段
            case 0x03A6:
                _hxData.TempSet[6] = value;
                break; // 温度七段
        }
    }

    /// <summary>
    ///     处理43_44的数据，来源HMI
    ///     实时值：温度一实际~温度七实际
    /// </summary>
    /// <param name="buff"></param>
    /// <param name="len"></param>
    private void DealData43_44(byte[] buff, int len)
    {
        if (len < 41)
            // 长度小于41,不合格43_44报文
            return;

        var offset = 5;
        int i;
        for (i = 0; i < 7; i++)
        {
            _hxData.TempAct[i] = GetShortValue(buff, offset);
            offset += 2;
        }
    }

    /// <summary>
    ///     处理45_43的数据，来源HMI
    ///     品质数据：
    ///     上模循环时间、上模射出时间、上模转保时间、上模储料时间、
    ///     上模关模计时、上模低压计时、上模高压计时、上模推力座位置、
    ///     上模开模计时、上模转保压力、上模射出起点、上模保压起点、
    ///     上模射出终点位置、上模射出监控位置、上模射出尖压、上模储料尖压、
    ///     上模最大射速、上模取件时间
    /// </summary>
    /// <param name="buff"></param>
    /// <param name="len"></param>
    /// <returns></returns>
    private int DealData45_43(byte[] buff, int len)
    {
        if (len == 56)
            return 1;
        if (len < 98)
            return 0;

        // 上模循环时间
        var offset = 5;
        _hxData.MsCycleTime = GetIntValue(buff, offset) / 100.0;

        // 上模射出时间
        offset += 4;
        _hxData.MsInjectionTime = GetIntValue(buff, offset) / 100.0;

        // 上模转保时间
        offset += 4;
        _hxData.MsHoldingTime = GetIntValue(buff, offset) / 100.0;

        // 上模储料时间
        offset += 4;
        _hxData.MsStorageTime = GetIntValue(buff, offset) / 100.0;

        // 上模关模计时
        offset += 4;
        _hxData.MsMoldCloseTime = GetShortValue(buff, offset) / 100.0;

        // 上模低压计时
        offset += 2;
        _hxData.MsLowPressureTime = GetShortValue(buff, offset) / 100.0;

        // 上模高压计时
        offset += 2;
        _hxData.MsHighPressureTime = GetShortValue(buff, offset) / 100.0;

        // 上模推力座位置
        offset += 2;
        _hxData.MsEjectorPos = GetShortValue(buff, offset) / 10.0;

        // 上模开模计时
        offset += 2;
        _hxData.MsMoldOpenTime = GetShortValue(buff, offset) / 100.0;

        // 上模转保压力
        offset += 2;
        _hxData.MsHoldingPressure = GetShortValue(buff, offset);

        // 上模射出起点
        offset += 2;
        _hxData.MsInjectionStartPos = GetShortValue(buff, offset) / 10.0;

        // 上模保压起点
        offset += 2;
        _hxData.MsHoldingStartPos = GetShortValue(buff, offset) / 10.0;

        // 上模射出终点位置
        offset += 2;
        _hxData.MsInjectionEndPos = GetShortValue(buff, offset) / 10.0;

        // 上模射出监控位置
        offset += 2;
        _hxData.MsInjectionMonPos = GetShortValue(buff, offset) / 10.0;

        // 上模射出尖压
        offset = 49;
        _hxData.MsInjectionPeakPressure = GetShortValue(buff, offset);

        // 上模储料尖压
        offset += 2;
        _hxData.MsStoragePeakPressure = GetShortValue(buff, offset);

        // 上模最大射速
        offset = 83;
        _hxData.MsInjectionSpeedMax = GetShortValue(buff, offset);

        // 上模取件时间
        offset += 2;
        _hxData.MsPickUpTime = GetIntValue(buff, offset) / 100.0;

        return 1;
    }

    /// <summary>
    ///     处理43_45的数据，来源HMI
    ///     实时值：输出压力、输出速度、输出背压、射出位置
    /// </summary>
    /// <param name="buff"></param>
    /// <param name="len"></param>
    private void DealData43_45(byte[] buff, int len)
    {
        if (len < 23)
            // 长度小于23,不合格43_45报文
            return;

        // 输出压力
        var offset = 33;
        _hxData.OutputPressure = GetShortValue(buff, offset);

        // 输出速度
        offset += 2;
        _hxData.OutputSpeed = GetShortValue(buff, offset);

        // 输出背压
        offset += 2;
        _hxData.OutputBackPressure = GetShortValue(buff, offset);

        // 射出位置实时值
        offset = 41;
        _hxData.InjectionPositionAct = GetShortValue(buff, offset) / 10.0;

        // 推力座位置
        offset += 2;
        _hxData.MoldPosition = GetShortValue(buff, offset) / 10.0;

        // 托模位置
        offset += 2;
        _hxData.EjectorPosition = GetShortValue(buff, offset) / 10.0;

        /*
        // 开模总数
        // HMI不一定发出开模数
        if (len >= 64)
        {
            hx_data.mold_opening_total = getIntValue(buff, 57);
        }
        */
    }

    /// <summary>
    ///     处理43_41的数据，来源HMI
    ///     实时值：设备状态（模式），告警信息
    /// </summary>
    /// <param name="buff"></param>
    /// <param name="len"></param>
    private void DealData43_41(byte[] buff, int len)
    {
        if (len < 33)
            // 长度小于33,不合格43_41报文
            return;

        // 模式
        _hxData.Mode = buff[6].ToString();


        /*
        // 开模总数
        // HMI不一定发出开模数
        if (len >= 64)
        {
            if (buff[55] == 0x01 && buff[56] == 0xA4)
            {
                hx_data.mold_opening_total_low = GetShortValue(buff, 57);
                hx_data.get_mold_opening_total_low = true;
            }

            if (buff[55] == 0x01 && buff[56] == 0xA5)
            {
                hx_data.mold_opening_total_high = GetShortValue(buff, 57);
                if (hx_data.get_mold_opening_total_low)
                {
                    hx_data.mold_opening_total = hx_data.mold_opening_total_low + (hx_data.mold_opening_total_high<<16);
                    hx_data.get_mold_opening_total_low = false;
                }
            }
        }
        */

        // 告警
        var alarm = new int[4];
        for (var i = 0; i < 4; i++) alarm[i] = GetIntValue(buff, 21 + i * 4);

        _hxData.AlarmStr = "";
        // 每bit代表一个告警，用","分割，根据实际情况处理
        for (var i = 0; i < 4; i++)
        for (var j = 0; j < 32; j++)
            if (GetBit(alarm[i], j))
            {
                if (_hxData.AlarmStr.Length == 0)
                {
                    _hxData.AlarmStr += _alarmTable[i * 32 + j];
                }
                else
                {
                    _hxData.AlarmStr += ",";
                    _hxData.AlarmStr += _alarmTable[i * 32 + j];
                }
            }

        //// 告警
        //int alarm[4] = { 0 };
        //for (i = 0; i < 4; i++)
        //{
        //    alarm[i] = getIntValue(buff, 21 + i * 4);
        //}

        //memset(hx_data.alarm_str, 0, sizeof(hx_data.alarm_str));
        //// 每bit代表一个告警，用","分割，根据实际情况处理
        //for (i = 0; i < 4; i++)
        //{
        //    for (j = 0; j < 32; j++)
        //    {
        //        if (get_bit(alarm[i], j))
        //        {
        //            if (strlen(hx_data.alarm_str) == 0)
        //            {
        //                strcat(hx_data.alarm_str, alarm_table[i * 32 + j]);
        //            }
        //            else
        //            {
        //                strcat(hx_data.alarm_str + strlen(hx_data.alarm_str), ",");
        //                strcat(hx_data.alarm_str + strlen(hx_data.alarm_str), alarm_table[i * 32 + j]);
        //            }
        //        }
        //    }
        //}
        // 全程计时
        _hxData.FullTime = GetIntValue(buff, 61) / 100.0;
    }

    /// <summary>
    ///     处理33_32的数据，来源控制器
    ///     实时值：开模总数
    /// </summary>
    /// <param name="buff"></param>
    /// <param name="len"></param>
    private void dealData33_32(byte[] buff, int len)
    {
        if (len < 850) return;

        // 开模总数
        var offset = 845;
        _hxData.MoldOpeningTotal = GetIntValue(buff, offset);
    }

    /// <summary>
    ///     处理33_33的数据，来源控制器
    ///     设定初始值，控制器上电，在和HMI握手通过之后，会发出一次（上电后只发出一次）
    /// </summary>
    /// <param name="buff"></param>
    /// <param name="len"></param>
    private void DealData33_33(byte[] buff, int len)
    {
        if (len < 1281)
            // 长度小于1281,不合格33_33报文
            return;
        // 开关模设定
        F2F2MoldSetting(buff);
        // 射出设定
        F3F2InjectionSetting(buff);
        // 储料设定
        F4F2StorageSetting(buff);
        // 托模设定
        F5F2EjectorSetting(buff);
        // 中子设定
        F6F2CoreSetting(buff);
        // 温度设定
        F8F2TempSetting(buff);
        // 座台设定
        F8F2SeatStandSetting(buff);
    }

    private bool GetBit(int x, int n)
    {
        return (x & (1 << n)) != 0;
    }

    // 从buff获取int型数据
    private int GetIntValue(byte[] buff, int offset)
    {
        var hex = new byte[4];
        hex[0] = buff[offset + 1];
        hex[1] = buff[offset];
        hex[2] = buff[offset + 3];
        hex[3] = buff[offset + 2];
        var value = BitConverter.ToInt32(hex, 0);
        return value;
    }

    //short GetShortValue(byte[] buff, int offset)
    //{
    //    byte[] hex = new byte[2];
    //    hex[0] = buff[offset + 1];
    //    hex[1] = buff[offset];
    //    short value = BitConverter.ToInt16(hex, 0);
    //    return value;
    //}

    private short GetShortValue(byte[] bytes, int index)
    {
        short value = 0;
        var hex = new byte[2];
        hex[0] = bytes[index + 1];
        hex[1] = bytes[index];

        value = BitConverter.ToInt16(hex, 0);
        return value;
        //
        // var value = (short) ((bytes[index] << 8) | bytes[index + 1]);
        // return value;
    }

    /// <summary>
    ///     温度设定F8F2
    /// </summary>
    /// <param name="buff"></param>
    private void F8F2TempSetting(byte[] buff)
    {
        var offset = 1861;
        int i;
        for (i = 0; i < 7; i++)
        {
            _hxData.TempSet[i] = GetShortValue(buff, offset);
            offset += 2;
        }
    }
    
    /// <summary>
    ///     座台设定
    /// </summary>
    /// <param name="buff"></param>
    private void F8F2SeatStandSetting(byte[] buff)
    {
        // 座台进动作时间
        _hxData.SeatAdvanceActionTime = GetShortValue(buff, 989) / 100.0;
        // 座台进一段压力
        _hxData.CoreAForwardPressure1 = GetShortValue(buff, 1573);
        // 座台进一段速度
        _hxData.CoreAForwardSpeed1 = GetShortValue(buff, 1575);
        // 座台进二段压力
        _hxData.CoreAForwardPressure2 = GetShortValue(buff, 1579);
        // 座台进二段速度
        _hxData.CoreAForwardSpeed2 = GetShortValue(buff, 1581);
        // 座台退一段压力
        _hxData.CoreAReversePressure1 = GetShortValue(buff, 1589);
        // 座台退一段速度
        _hxData.CoreAReverseSpeed1 = GetShortValue(buff, 1591);
        // 座台退动作时间
        _hxData.CoreAReverseTime = GetShortValue(buff, 1597);
    }

    /// <summary>
    ///     中子设定F6F2
    /// </summary>
    /// <param name="buff"></param>
    private void F6F2CoreSetting(byte[] buff)
    {
        // 中子A进动作位置
        var offset = 397;
        _hxData.CoreAForwardPosition = GetShortValue(buff, offset) / 10.0;

        // 中子A进压力
        offset += 2;
        _hxData.CoreAForwardPressure = GetShortValue(buff, offset);

        // 中子A进速度
        offset += 2;
        _hxData.CoreAForwardSpeed = GetShortValue(buff, offset);

        // 中子A进动作时间
        offset += 2;
        _hxData.CoreAForwardTime = GetShortValue(buff, offset) / 100.0;

        // 中子A进绞牙计数
        offset += 2;
        _hxData.CoreAForwardCount = GetShortValue(buff, offset);

        // 中子A退动作位置
        offset = 427;
        _hxData.CoreABackwardPosition = GetShortValue(buff, offset) / 10.0;

        // 中子A退压力
        offset += 2;
        _hxData.CoreABackwardPressure = GetShortValue(buff, offset);

        // 中子A退速度
        offset += 2;
        _hxData.CoreABackwardSpeed = GetShortValue(buff, offset);

        // 中子A退动作时间
        offset += 2;
        _hxData.CoreABackwardTime = GetShortValue(buff, offset) / 100.0;

        // 中子A退绞牙计数
        offset += 2;
        _hxData.CoreABackwardCount = GetShortValue(buff, offset);

        // 中子A退绞牙退二
        offset = 447;
        _hxData.CoreABackward2 = GetShortValue(buff, offset);


        // 中子B进动作位置
        offset = 461;
        _hxData.CoreBForwardPosition = GetShortValue(buff, offset) / 10.0;

        // 中子B进压力
        offset += 2;
        _hxData.CoreBForwardPressure = GetShortValue(buff, offset);

        // 中子B进速度
        offset += 2;
        _hxData.CoreBForwardSpeed = GetShortValue(buff, offset);

        // 中子B进动作时间
        offset += 2;
        _hxData.CoreBForwardTime = GetShortValue(buff, offset) / 100.0;

        // 中子B进绞牙计数
        offset += 2;
        _hxData.CoreBForwardCount = GetShortValue(buff, offset);

        offset = 491;
        // 中子B退动作位置
        _hxData.CoreBBackwardPosition = GetShortValue(buff, offset) / 10.0;

        // 中子B退压力
        offset += 2;
        _hxData.CoreBBackwardPressure = GetShortValue(buff, offset);

        // 中子B退速度
        offset += 2;
        _hxData.CoreBBackwardSpeed = GetShortValue(buff, offset);

        // 中子B退动作时间
        offset += 2;
        _hxData.CoreBBackwardTime = GetShortValue(buff, offset) / 100.0;

        // 中子B退绞牙计数
        offset += 2;
        _hxData.CoreBBackwardCount = GetShortValue(buff, offset);


        // 中子C进动作位置
        offset = 525;
        _hxData.CoreCForwardPosition = GetShortValue(buff, offset) / 10.0;

        // 中子C进压力
        offset += 2;
        _hxData.CoreCForwardPressure = GetShortValue(buff, offset);

        // 中子C进速度
        offset += 2;
        _hxData.CoreCForwardSpeed = GetShortValue(buff, offset);

        // 中子C进动作时间
        offset += 2;
        _hxData.CoreCForwardTime = GetShortValue(buff, offset) / 100.0;

        // 中子C进绞牙计数
        offset += 2;
        _hxData.CoreCForwardCount = GetShortValue(buff, offset);


        // 中子C退动作位置
        offset = 555;
        _hxData.CoreCBackwardPosition = GetShortValue(buff, offset) / 10.0;

        // 中子C退压力
        offset += 2;
        _hxData.CoreCBackwardPressure = GetShortValue(buff, offset);

        // 中子C退速度
        offset += 2;
        _hxData.CoreCBackwardSpeed = GetShortValue(buff, offset);

        // 中子C退动作时间
        offset += 2;
        _hxData.CoreCBackwardTime = GetShortValue(buff, offset) / 100.0;

        // 中子C退绞牙计数
        offset += 2;
        _hxData.CoreCBackwardCount = GetShortValue(buff, offset);


        // 中子D进动作位置
        offset = 589;
        _hxData.CoreDForwardPosition = GetShortValue(buff, offset) / 10.0;

        // 中子D进压力
        offset += 2;
        _hxData.CoreDForwardPressure = GetShortValue(buff, offset);

        // 中子D进速度
        offset += 2;
        _hxData.CoreDForwardSpeed = GetShortValue(buff, offset);

        // 中子D进动作时间
        offset += 2;
        _hxData.CoreDForwardTime = GetShortValue(buff, offset) / 100.0;

        // 中子D进绞牙计数
        offset += 2;
        _hxData.CoreDForwardCount = GetShortValue(buff, offset);


        // 中子D退动作位置
        offset = 619;
        _hxData.CoreDBackwardPosition = GetShortValue(buff, offset) / 10.0;

        // 中子D退压力
        offset += 2;
        _hxData.CoreDBackwardPressure = GetShortValue(buff, offset);

        // 中子D退速度
        offset += 2;
        _hxData.CoreDBackwardSpeed = GetShortValue(buff, offset);

        // 中子D退动作时间
        offset += 2;
        _hxData.CoreDBackwardTime = GetShortValue(buff, offset) / 100.0;

        // 中子D退绞牙计数
        offset += 2;
        _hxData.CoreDBackwardCount = GetShortValue(buff, offset);
    }

    /// <summary>
    ///     托模设定F5F2
    /// </summary>
    /// <param name="buff"></param>
    private void F5F2EjectorSetting(byte[] buff)
    {
        var offset = 837;
        int i;
        // 托模压力，速度，位置
        for (i = 0; i < 4; i++)
        {
            if (i <= 1)
                _hxData.EjectorForwardPressure[i] = GetShortValue(buff, offset);
            else
                _hxData.EjectorBackwardPressure[i - 2] = GetShortValue(buff, offset);

            offset += 2;
            if (i <= 1)
                _hxData.EjectorForwardSpeed[i] = GetShortValue(buff, offset);
            else
                _hxData.EjectorBackwardSpeed[i - 2] = GetShortValue(buff, offset);

            offset += 2;
            if (i <= 1)
                _hxData.EjectorForwardPosition[i] = GetShortValue(buff, offset) / 10.0;
            else
                _hxData.EjectorBackwardPosition[i - 2] = GetShortValue(buff, offset) / 10.0;

            offset += 2;
        }

        // 托模进延时时间
        offset = 865;
        _hxData.EjectorForwardDelayTime = GetShortValue(buff, offset) / 10.0;

        // 托模退延时时间
        offset += 2;
        _hxData.EjectorBackwardDelayTime = GetShortValue(buff, offset) / 10.0;
    }

    /// <summary>
    ///     储料设定，包括储料和射退
    /// </summary>
    /// <param name="buff"></param>
    private void F4F2StorageSetting(byte[] buff)
    {
        var offset = 165;
        int i;
        for (i = 0; i < 5; i++)
        {
            // 储料压力
            _hxData.StoragePressure[i] = GetShortValue(buff, offset);

            // 储料背压
            offset += 2;
            _hxData.StorageBackPressure[i] = GetShortValue(buff, offset);

            // 储料速度
            offset += 2;
            _hxData.StorageSpeed[i] = GetShortValue(buff, offset);

            // 储料位置
            offset += 2;
            _hxData.StoragePosition[i] = GetShortValue(buff, offset) / 10.0;

            offset += 2;
        }

        // 储前冷却
        offset = 373;
        _hxData.CoolingBeforeStorage = GetShortValue(buff, offset) / 100.0;

        // 射退压力
        offset = 1829;
        _hxData.InjectionBackPressure = GetShortValue(buff, offset);

        // 射退速度
        offset = 1831;
        _hxData.InjectionBackSpeed = GetShortValue(buff, offset);

        // 射退距离
        offset = 1835;
        _hxData.InjectionBackDistance = GetShortValue(buff, offset) / 10.0;

        // 射退模式
        offset = 1839;
        _hxData.InjectionBackMode = GetShortValue(buff, offset);

        // 储前射退距离
        offset = 1843;
        _hxData.InjectionBackDistanceBeforeStorage = GetShortValue(buff, offset) / 10.0;
    }

    /// <summary>
    ///     射出设定F3F2
    /// </summary>
    /// <param name="buff"></param>
    private void F3F2InjectionSetting(byte[] buff)
    {
        // F3射出_F2 射出--- 射出设定 （下半段）
        var offset = 1189;
        int i;
        // 保压部分
        for (i = 0; i < 6; i++)
        {
            // 保压压力
            _hxData.HoldingPressure[i] = GetShortValue(buff, offset);

            // 保压速度
            offset += 2;
            _hxData.HoldingSpeed[i] = GetShortValue(buff, offset);

            // 保压时间
            offset += 2;
            _hxData.HoldingTime[i] = GetShortValue(buff, offset) / 100.0;

            offset += 4;
        }

        // 射出部分
        // 转保压压力
        offset = 1269;
        _hxData.SwitchHoldingPressure = GetShortValue(buff, offset);

        // 转保压位置
        offset += 2;
        _hxData.SwitchHoldingPosition = GetShortValue(buff, offset) / 10.0;

        // 转保压选择，0表示位置，1表示时间，2表示压力
        offset += 2;
        _hxData.SwitchHoldingSelection = GetShortValue(buff, offset);

        // 射出一段压力
        offset = 1317;
        _hxData.InjectionPressure[0] = GetShortValue(buff, offset);

        // 射出一段速度
        offset += 2;
        _hxData.InjectionSpeed[0] = GetShortValue(buff, offset);

        // 射出一段位置不存在
        _hxData.InjectionPosition[0] = 0.0;

        // 转保压时间设定
        offset += 2;
        _hxData.SwitchHoldingTime = GetShortValue(buff, offset) / 100.0;

        // 二段~六段
        offset = 1325; // 2段起步
        for (i = 1; i < 6; i++)
        {
            // 射出位置
            _hxData.InjectionPosition[i] = GetShortValue(buff, offset) / 10.0;

            // 射出压力
            offset += 4;
            _hxData.InjectionPressure[i] = GetShortValue(buff, offset);

            // 射出速度
            offset += 2;
            _hxData.InjectionSpeed[i] = GetShortValue(buff, offset);

            offset += 6;
        }
    }

    /// <summary>
    ///     开关模设定F2F2
    /// </summary>
    /// <param name="buff"></param>
    private void F2F2MoldSetting(byte[] buff)
    {
        // 关模
        var offset = 261;
        int i;
        for (i = 0; i < 5; i++)
        {
            // 关模压力
            _hxData.MoldClosingPressure[i] = GetShortValue(buff, offset);
            // 关模速度
            offset += 2;
            _hxData.MoldClosingSpeed[i] = GetShortValue(buff, offset);

            // 关模位置，可能有问题，对不上，原因未知
            offset += 2;
            _hxData.MoldClosingPosition[i] = GetShortValue(buff, offset) / 10.0;

            offset += 2;
        }

        // 模具冷却时间
        offset = 377;
        _hxData.MoldCoolingTime = GetIntValue(buff, offset) / 100.0;

        // 再循环计时
        offset = 1517;
        _hxData.RecyclingTime = GetShortValue(buff, offset) / 100.0;

        // 开模
        offset = 1637;
        for (i = 0; i < 5; i++)
        {
            // 开模压力
            _hxData.MoldOpeningPressure[i] = GetShortValue(buff, offset);

            // 开模速度
            offset += 2;
            _hxData.MoldOpeningSpeed[i] = GetShortValue(buff, offset);

            // 开模位置，可能有问题，对不上，原因未知
            offset += 2;
            _hxData.MoldOpeningPosition[i] = GetShortValue(buff, offset) / 10.0;

            offset += 2;
        }

        // 开模行程
        offset = 1665;
        _hxData.MoldOpeningStroke = GetShortValue(buff, offset) / 10.0;
    }

    // 告警列表
    private readonly string[] _alarmTable =
    {
        "温度偏差",
        "安全门未关",
        "请开安全门",
        "放开手动／急停键",
        "油温偏差",
        "全程未定时完成",
        "加热器电流错误",
        "托模未到定位",
        "射出监控失败",
        "射出防护罩未关",
        "机械手失败",
        "开模数已到",
        "开模未到定位",
        "托模失败",
        "储料未定时完成",
        "关模未到定位",
        "位置设定不良",
        "完成自动调模",
        "润滑油位偏差",
        "射出起始位置偏差",
        "储料转速偏差",
        "射出时间偏差",
        "开模一慢未定位",
        "关模保护失败",
        "射退终未到定位",
        "储料终未到定位",
        "开模装数已到",
        "中子未到定位",
        "机械安全异常",
        "润滑检出失败",
        "油量检出失败",
        "滤纲检出失败",
        "位置检测不良",
        "调模终已到",
        "模子监控失败",
        "中子动作位置偏",
        "B 中子位设不良",
        "侯空压机",
        "调模电眼失败",
        "冷却源压力偏低",
        "机械手急停",
        "等待机械手",
        "请按关模键",
        "托退未到定位",
        "液压安全异常",
        "请按安全确认键",
        "限位失败",
        "马达启动失败",
        "请按启动键",
        "背面安全门未关",
        "调模锁定",
        "手动开模／托模",
        "马达过载",
        "关模限动",
        "调模保护失败",
        "手动开模",
        "温度没有上升",
        "请离开安全踏板",
        "模板失压",
        "请按手动键",
        "上顶盖未关",
        "请开安全门二",
        "安全门二未关",
        "机械防护中",
        "模具太小",
        "模具太大",
        "原点确认错误",
        "移模入限位",
        "移模出限位",
        "QMC 紧急停止",
        "移模出完成",
        "移模入完成",
        "销子退未到定位",
        "销子进未到定位",
        "模内无模具",
        "平台有模具",
        "模内有模具",
        "平台无模具",
        "连线位完成",
        "警报五-15",
        "漏电保护异常",
        "储能失败",
        "安全门检测异常",
        "後安全门检测异常",
        "自动门安全异常",
        "储压过高",
        "油温过高",
        "料斗检知异常",
        "防冷启动时间未到",
        "电热马达未启动",
        "回油堵塞",
        "允许加料",
        "锁模力偏差",
        "输入调模吨数一",
        "保压未到转换位置",
        "气源压力低",
        "外部警报",
        "高压滤网异常",
        "夹具异常",
        "转模电眼失败",
        "模腔压力异常",
        "模具未顶退",
        "料斗检知异常#2",
        "警报七-07",
        "锁模力未校正",
        "输入调模吨数二",
        "第二组润滑检出失败",
        "放开安全确认键",
        "中子状态异常",
        "系统已自动转入半温, 请按手动键恢复",
        "电机温度异常",
        "模具厚度异常"
    };


    public void Dispose()
    {
        _tokenSource.Cancel();
        Thread.Sleep(200);
        _tcpClient?.Close();
        _tcpClient?.Dispose();
        _serialPort?.Close();
        _serialPort?.Dispose();
    }
}