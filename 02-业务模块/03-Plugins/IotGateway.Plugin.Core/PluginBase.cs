using IotGateway.Plugin.Core.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace IotGateway.Plugin.Core;

/// <summary>
/// 插件基类
/// </summary>
public abstract class PluginBase : IPlugin
{
  /// <summary>
  /// 是否启用  
  /// </summary>
  protected bool _enabled;

  /// <summary>
  /// 配置
  /// </summary>
  protected object _configuration;

  /// <summary>
  /// DNC配置集合
  /// </summary>
  protected List<DncConfig> _dncConfigs = new List<DncConfig>();

  /// <summary>
  /// 插件名称
  /// </summary>
  public abstract string Name { get; }

  /// <summary>
  /// 插件版本
  /// </summary>
  public abstract string Version { get; }

  /// <summary>
  /// 插件描述
  /// </summary>
  public abstract string Description { get; }

  /// <summary>
  /// 插件分类
  /// </summary>
  public virtual string Category { get; } = "通用";

  /// <summary>
  /// 是否启用
  /// </summary>
  public bool Enabled => _enabled;

  /// <summary>
  /// 初始化
  /// </summary>
  public virtual async Task InitializeAsync()
  {
    // 基础初始化逻辑
    await Task.CompletedTask;
  }

  /// <summary>
  /// 启动
  /// </summary>
  public virtual async Task StartAsync()
  {
    _enabled = true;
    await Task.CompletedTask;
  }

  /// <summary>
  /// 停止
  /// </summary>
  public virtual async Task StopAsync()
  {
    _enabled = false;
    await Task.CompletedTask;
  }

  /// <summary>
  /// 获取配置
  /// </summary>
  public virtual async Task<object> GetConfigurationAsync()
  {
    return await Task.FromResult(_configuration);
  }

  /// <summary>
  /// 获取配置Schema
  /// </summary>
  public virtual async Task<object?> GetConfigurationSchemaAsync()
  {
    // 先尝试调用GetConfigurationSchema方法
    if (_configuration is IPluginConfig config)
    {
      var getSchema = config.GetConfigurationSchema();
      if (getSchema != null)
        return await Task.FromResult(getSchema);
    }
    return null;
  }

  /// <summary>
  /// 更新配置
  /// </summary>
  public virtual async Task UpdateConfigurationAsync(object configuration)
  {
    if (_configuration != null)
    {
      var configType = _configuration.GetType();
      if (configuration is System.Text.Json.JsonElement jsonElement)
      {
        // 如果是 JsonElement，将其转换为原始配置类型
        _configuration = System.Text.Json.JsonSerializer.Deserialize(
          jsonElement.GetRawText(),
          configType,
          new System.Text.Json.JsonSerializerOptions
          {
            PropertyNameCaseInsensitive = true
          }
        );
      }
      else
      {
        // 如果不是 JsonElement，尝试直接转换
        _configuration = System.Text.Json.JsonSerializer.Deserialize(
          System.Text.Json.JsonSerializer.Serialize(configuration),
          configType,
          new System.Text.Json.JsonSerializerOptions
          {
            PropertyNameCaseInsensitive = true
          }
        );
      }
    }
    else
    {
      _configuration = configuration;
    }

    await Task.CompletedTask;
  }

  /// <summary>
  /// 启用或禁用插件
  /// </summary>
  /// <param name="enable">是否启用</param>
  /// <returns>操作结果</returns>
  public virtual async Task ToggleEnableAsync(bool enable)
  {
    if (enable && !_enabled)
    {
      // 如果需要启用且当前未启用，则启动插件
      await StartAsync();
    }
    else if (!enable && _enabled)
    {
      // 如果需要禁用且当前已启用，则停止插件
      await StopAsync();
    }

    // 更新配置中的启用状态（如果配置实现了IPluginConfig接口）
    if (_configuration is IPluginConfig config)
    {
      config.Enabled = enable;

      // 更新配置
      await UpdateConfigurationAsync(_configuration);
    }
  }

  /// <summary>
  /// 获取FTP服务器文件列表
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="folderType">文件夹类型(SEND/REC)</param>
  /// <returns>以文件夹为键，文件信息列表为值的字典</returns>
  public virtual async Task<List<FtpFileInfo>> GetFtpFiles(string? configId = null, string? folderType = null)
  {
    var result = new List<FtpFileInfo>();
    var pluginName = GetType().Name;

    try
    {
      // 获取DNC配置列表
      var dncConfigs = await GetDncConfigsAsync();

      // 如果指定了configId，只处理该配置
      if (!string.IsNullOrEmpty(configId))
      {
        var targetConfig = dncConfigs.FirstOrDefault(c => c.Id.ToString() == configId);
        if (targetConfig != null)
        {
          dncConfigs = new List<DncConfig> { targetConfig };
        }
        else
        {
          return result;
        }
      }

      var enabledConfigs = dncConfigs.Where(c => c.Enabled).ToList();

      // 遍历每个DNC配置
      foreach (var dncConfig in enabledConfigs)
      {
        // 处理SEND文件夹
        if (string.IsNullOrEmpty(folderType) || folderType.ToUpper() == "SEND")
        {
          var sendPath = GetFtpDirectoryPath(dncConfig, "SEND");

          if (!string.IsNullOrEmpty(sendPath))
          {
            var sendFiles = GetFilesFromDirectory(sendPath, "SEND", dncConfig);

            if (sendFiles.Any())
            {
              result = sendFiles;
            }
          }
        }

        // 处理REC文件夹
        if (string.IsNullOrEmpty(folderType) || folderType.ToUpper() == "REC")
        {
          var recPath = GetFtpDirectoryPath(dncConfig, "REC");

          if (!string.IsNullOrEmpty(recPath))
          {
            var recFiles = GetFilesFromDirectory(recPath, "REC", dncConfig);

            if (recFiles.Any())
            {
              result = recFiles;
            }
          }
        }
      }
    }
    catch (Exception ex)
    {
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 错误: 获取FTP文件列表时出错");
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 异常信息: {ex.Message}");
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 堆栈跟踪: {ex.StackTrace}");
    }

    return result;
  }

  /// <summary>
  /// 获取FTP目录路径（由子类重写以提供具体实现）
  /// </summary>
  /// <param name="dncConfig">DNC配置</param>
  /// <param name="folderType">文件夹类型</param>
  /// <returns>目录路径</returns>
  protected virtual string GetFtpDirectoryPath(DncConfig dncConfig, string folderType)
  {
    var pluginName = GetType().Name;

    // 基类提供默认实现：RootFiles/{设备编码}/{文件夹类型}
    var defaultPath = folderType.ToUpper() switch
    {
      "SEND" => $"RootFiles/{dncConfig.DeviceCode}/SEND",
      "REC" => $"RootFiles/{dncConfig.DeviceCode}/REC",
      _ => string.Empty
    };

    if (!string.IsNullOrEmpty(defaultPath))
    {
      var fullPath = Path.Combine(AppContext.BaseDirectory, defaultPath);
      return fullPath;
    }

    return string.Empty;
  }

  /// <summary>
  /// 从指定目录获取文件列表
  /// </summary>
  /// <param name="directoryPath">目录路径</param>
  /// <param name="folderType">文件夹类型</param>
  /// <param name="dncConfig">DNC配置</param>
  /// <returns>文件信息列表</returns>
  protected virtual List<FtpFileInfo> GetFilesFromDirectory(string directoryPath, string folderType, DncConfig dncConfig)
  {
    var fileList = new List<FtpFileInfo>();
    var pluginName = GetType().Name;

    try
    {
      // 确保目录存在
      if (!Directory.Exists(directoryPath))
      {
        Directory.CreateDirectory(directoryPath);
        return fileList; // 新创建的目录为空
      }

      // 获取目录中的所有文件
      var files = Directory.GetFiles(directoryPath);

      // 处理每个文件，提取详细信息
      foreach (var file in files)
      {
        try
        {
          var fileInfo = new FileInfo(file);

          // 创建文件信息模型
          var fileModel = new FtpFileInfo
          {
            FileName = fileInfo.Name,
            ModifiedTime = fileInfo.LastWriteTime,
            Size = FormatFileSize(fileInfo.Length),
            FileType = fileInfo.Extension,
            FullPath = fileInfo.FullName,
            Source = directoryPath,
            ConfigId = dncConfig.Id.ToString(),
            DeviceCode = dncConfig.DeviceCode,
            FolderType = folderType
          };

          fileList.Add(fileModel);
        }
        catch (Exception ex)
        {
          Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 错误: 处理文件时出错");
          Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 文件路径: {file}");
          Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 错误信息: {ex.Message}");
        }
      }
    }
    catch (Exception ex)
    {
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 错误: 读取目录时出错");
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 目录路径: {directoryPath}");
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 错误信息: {ex.Message}");
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 堆栈跟踪: {ex.StackTrace}");
    }

    return fileList;
  }

  /// <summary>
  /// 格式化文件大小
  /// </summary>
  /// <param name="bytes">字节数</param>
  /// <returns>格式化后的文件大小</returns>
  protected virtual string FormatFileSize(long bytes)
  {
    if (bytes < 1024)
      return $"{bytes} B";
    else if (bytes < 1024 * 1024)
      return $"{bytes / 1024.0:F1} KB";
    else if (bytes < 1024 * 1024 * 1024)
      return $"{bytes / (1024.0 * 1024.0):F1} MB";
    else
      return $"{bytes / (1024.0 * 1024.0 * 1024.0):F1} GB";
  }

  /// <summary>
  /// 获取FTP服务器文件内容
  /// </summary>
  /// <param name="filePath">文件路径</param>
  /// <returns>文件内容</returns>
  public virtual async Task<string> GetFileContent(string filePath)
  {
    var pluginName = GetType().Name;

    try
    {

      // 检查文件路径是否为空
      if (string.IsNullOrWhiteSpace(filePath))
      {
        var errorMsg = "文件路径不能为空";
        return $"错误: {errorMsg}";
      }

      // 检查文件是否存在
      if (!File.Exists(filePath))
      {
        var errorMsg = $"文件不存在: {filePath}";
        return $"错误: {errorMsg}";
      }

      // 获取文件信息
      var fileInfo = new FileInfo(filePath);

      // 检查文件大小，防止加载过大的文件（限制为10MB）
      const long maxFileSize = 10 * 1024 * 1024; // 10MB
      if (fileInfo.Length > maxFileSize)
      {
        var errorMsg = $"文件过大，无法读取。文件大小: {FormatFileSize(fileInfo.Length)}，最大限制: {FormatFileSize(maxFileSize)}";
        return $"错误: {errorMsg}";
      }

      // 读取文件内容
      var content = await File.ReadAllTextAsync(filePath);

      return content;
    }
    catch (UnauthorizedAccessException ex)
    {
      var errorMsg = $"没有权限访问文件: {filePath}";
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 错误: {errorMsg} - {ex.Message}");
      return $"错误: {errorMsg}";
    }
    catch (IOException ex)
    {
      var errorMsg = $"读取文件时发生IO错误: {ex.Message}";
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 错误: {errorMsg}");
      return $"错误: {errorMsg}";
    }
    catch (Exception ex)
    {
      var errorMsg = $"读取文件时发生未知错误: {ex.Message}";
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 错误: {errorMsg}");
      return $"错误: {errorMsg}";
    }
  }

  /// <summary>
  /// 设置DNC配置
  /// </summary>
  /// <param name="dncConfig">DNC配置</param>
  /// <returns>设置结果</returns>
  public virtual async Task SetDncConfigAsync(DncConfig dncConfig)
  {
    if (dncConfig == null)
      return;

    // 检查是否已存在相同ID的配置
    var existingConfig = _dncConfigs.FirstOrDefault(c => c.Id == dncConfig.Id);
    if (existingConfig != null)
    {
      // 更新已存在的配置
      var index = _dncConfigs.IndexOf(existingConfig);
      _dncConfigs[index] = dncConfig;
    }
    else
    {
      // 添加新配置
      _dncConfigs.Add(dncConfig);
    }

    await Task.CompletedTask;
  }

  /// <summary>
  /// 获取DNC配置
  /// </summary>
  /// <returns>DNC配置集合</returns>
  public virtual async Task<List<DncConfig>> GetDncConfigsAsync()
  {
    return await Task.FromResult(_dncConfigs);
  }

  /// <summary>
  /// 获取DNC配置
  /// </summary>
  /// <returns>DNC配置，如有多个则返回第一个</returns>
  public virtual async Task<DncConfig> GetDncConfigAsync()
  {
    return await Task.FromResult(_dncConfigs.FirstOrDefault());
  }

  /// <summary>
  /// 获取指定设备编码的DNC配置
  /// </summary>
  /// <param name="deviceCode">设备编码</param>
  /// <returns>DNC配置</returns>
  public virtual async Task<DncConfig> GetDncConfigByDeviceCodeAsync(string deviceCode)
  {
    return await Task.FromResult(_dncConfigs.FirstOrDefault(c => c.DeviceCode == deviceCode));
  }

  /// <summary>
  /// 移除DNC配置
  /// </summary>
  /// <param name="id">配置ID</param>
  /// <returns>操作结果</returns>
  public virtual async Task RemoveDncConfigAsync(long id)
  {
    var config = _dncConfigs.FirstOrDefault(c => c.Id == id);
    if (config != null)
    {
      _dncConfigs.Remove(config);
    }

    await Task.CompletedTask;
  }
}