using Feng.Common.OpcUaHelper;
using Feng.IotGateway.Core.Service.Cache;
using IotGateway.EdgeDevice.TransPonds.SendToOpcUa.Dto;
using Opc.Ua;

namespace IotGateway.EdgeDevice.TransPonds.SendToOpcUa;

/// <summary>
///     转发配置-OpcUa
///     版 本:V3.0.7.2
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-05-15
/// </summary>
[ApiDescriptionSettings("转发配置")]
public class OpcUaClientService : ITransient, IDynamicApiController
{
    private readonly CacheService _cacheService;
    private readonly SqlSugarRepository<Feng.IotGateway.Core.Entity.Driver> _driver;

    public OpcUaClientService(CacheService cacheService, SqlSugarRepository<Feng.IotGateway.Core.Entity.Driver> driver)
    {
        _cacheService = cacheService;
        _driver = driver;
    }

    /// <summary>
    ///     opcDa节点列表 - 只获取指定节点的直接子节点
    /// </summary>
    /// <returns></returns>
    [HttpGet("/opcDa/nodeSelect")]
    [HttpGet("/api/opcDa/nodeSelect")]
    public async Task<List<OpcUaNodeTree>> NodeSelect([FromQuery] NodeSelectInput input)
    {
        var totalStart = DateTime.Now;

        try
        {
            Console.WriteLine("[NodeSelect] ========== 开始处理请求 ==========");
            Console.WriteLine($"[NodeSelect] URL: {input.Url}");
            Console.WriteLine($"[NodeSelect] NodeId: {input.NodeId ?? "null"}");
            Console.WriteLine($"[NodeSelect] ReadDataType: {input.ReadDataType}");
            Console.WriteLine($"[NodeSelect] UseOriginalBrowse: {input.UseOriginalBrowse}");

            var opcDa = new OpcUaClientHelper();
            Console.WriteLine("[NodeSelect] 创建OpcUaClientHelper成功");

            var connectStart = DateTime.Now;
            Console.WriteLine("[NodeSelect] 开始连接服务器");
            await opcDa.ConnectServerFast(input.Url);
            var connectTime = (DateTime.Now - connectStart).TotalMilliseconds;

            if (!opcDa.Connected)
            {
                Console.WriteLine($"服务连接失败，耗时: {connectTime:F0}ms");
                return new List<OpcUaNodeTree>();
            }

            Console.WriteLine($"连接成功，耗时: {connectTime:F0}ms");

            if (input.NodeId.IsNullOrEmpty()) input.NodeId = ObjectIds.ObjectsFolder.ToString();

            Console.WriteLine("[NodeSelect] 开始浏览节点");
            
            Console.WriteLine("[NodeSelect] 开始浏览节点");

            // 根据参数选择浏览方法
            var browseStart = DateTime.Now;
            List<ReferenceDescription> childNodes;

            if (input.UseOriginalBrowse)
            {
                Console.WriteLine("[NodeSelect] 🔄 使用原始浏览方法（FormUtils.Browse + 双重浏览）");
                childNodes = opcDa.BrowseNodeReference(input.NodeId).Where(w => w.BrowseName.Name != "Server").ToList();
            }
            else
            {
                Console.WriteLine("[NodeSelect] ⚡ 使用优化浏览方法（Session.Browse + 单次浏览）");
                childNodes = opcDa.BrowseNodeReferenceOptimized(input.NodeId).Where(w => w.BrowseName.Name != "Server").ToList();
            }

            var browseTime = (DateTime.Now - browseStart).TotalMilliseconds;
            Console.WriteLine($"[NodeSelect] 浏览完成，耗时: {browseTime:F0}ms，找到 {childNodes.Count} 个子节点");

            Console.WriteLine("[NodeSelect] 开始创建节点树结构");
            var result = new List<OpcUaNodeTree>();

            // 创建直接子节点列表
            foreach (var node in childNodes)
            {
                var treeNode = new OpcUaNodeTree
                {
                    NodeId = node.NodeId.ToString(),
                    NodeClass = node.NodeClass,
                    BrowseName = node.BrowseName.Name
                };

                result.Add(treeNode);
            }

            Console.WriteLine($"[NodeSelect] 节点树创建完成，共 {result.Count} 个节点");

            // 统计节点类型
            var objectCount = result.Count(n => n.NodeClass == NodeClass.Object);
            var variableCount = result.Count(n => n.NodeClass == NodeClass.Variable);
            var methodCount = result.Count(n => n.NodeClass == NodeClass.Method);
            Console.WriteLine($"[NodeSelect] 节点类型统计 - Object: {objectCount}, Variable: {variableCount}, Method: {methodCount}");

            // 只有在明确要求时才读取数据类型（性能考虑）
            if (input.ReadDataType && result.Any(n => n.NodeClass == NodeClass.Variable))
            {
                Console.WriteLine("[NodeSelect] 开始读取变量节点数据类型");
                var variableNodes = result.Where(n => n.NodeClass == NodeClass.Variable)
                    .Select(n => n.NodeId).ToList();
                var nodeMap = result.Where(n => n.NodeClass == NodeClass.Variable)
                    .ToDictionary(n => n.NodeId, n => n);

                Console.WriteLine($"[NodeSelect] 需要读取数据类型的变量节点数: {variableNodes.Count}");
                var dataTypeStart = DateTime.Now;
                BatchReadDataTypes(opcDa, variableNodes, nodeMap);
                var dataTypeTime = (DateTime.Now - dataTypeStart).TotalMilliseconds;
                Console.WriteLine($"[NodeSelect] 数据类型读取完成，耗时: {dataTypeTime:F0}ms");
            }
            else
            {
                Console.WriteLine($"[NodeSelect] 跳过数据类型读取 (ReadDataType={input.ReadDataType}, 变量节点数={result.Count(n => n.NodeClass == NodeClass.Variable)})");
            }

            Console.WriteLine("[NodeSelect] 开始断开连接");
            opcDa.Disconnect();
            var totalTime = (DateTime.Now - totalStart).TotalMilliseconds;
            Console.WriteLine("[NodeSelect] 连接已断开");
            Console.WriteLine("[NodeSelect] ========== 请求处理完成 ==========");
            Console.WriteLine($"[NodeSelect] 总耗时: {totalTime:F0}ms，成功获取 {result.Count} 个子节点");


            Console.WriteLine("[NodeSelect] 返回传统结果");
            return result;
        }
        catch (Exception ex)
        {
            var totalTime = (DateTime.Now - totalStart).TotalMilliseconds;
            Console.WriteLine($"[NodeSelect] ❌ 异常:{ex.Message}，耗时: {totalTime:F0}ms");
            return
                new List<OpcUaNodeTree>();
        }
    }

    /// <summary>
    ///     opcDa测试连接
    /// </summary>
    /// <returns></returns>
    [HttpPost("/transPond/opcDa/connect")]
    [HttpPost("/api/transPond/opcDa/connect")]
    public async Task<bool> Connect(NodeSelectInput input)
    {
        // 释放连接，不能直接使用连接状态判断
        var connect = false;
        var opcDa = new OpcUaClientHelper();
        await opcDa.ConnectServer(input.Url);
        if (opcDa.Connected)
            connect = true;
        opcDa.Disconnect();
        return connect;
    }


    /// <summary>
    ///     批量读取变量节点的数据类型
    /// </summary>
    /// <param name="opcDa">OPC UA客户端</param>
    /// <param name="variableNodes">变量节点ID列表</param>
    /// <param name="nodeMap">节点映射字典</param>
    private void BatchReadDataTypes(OpcUaClientHelper opcDa, List<string> variableNodes, Dictionary<string, OpcUaNodeTree> nodeMap)
    {
        if (variableNodes.Count == 0) return;

        try
        {
            // 减少批次大小，提升响应速度
            const int batchSize = 20;
            var totalBatches = (int)Math.Ceiling((double)variableNodes.Count / batchSize);

            for (var i = 0; i < variableNodes.Count; i += batchSize)
            {
                var currentBatch = i / batchSize + 1;
                var batch = variableNodes.Skip(i).Take(batchSize).ToArray();
                Console.WriteLine($"处理批次 {currentBatch}/{totalBatches}，节点数: {batch.Length}");

                try
                {
                    var nodeIds = batch.Select(id => new NodeId(id)).ToArray();
                    var dataValues = opcDa.ReadNodes(nodeIds);

                    for (var j = 0; j < batch.Length && j < dataValues.Count; j++)
                        if (nodeMap.TryGetValue(batch[j], out var treeNode) && dataValues[j] != null)
                            treeNode.DataType = dataValues[j].WrappedValue.TypeInfo?.BuiltInType.ToString();
                }
                catch (Exception batchEx)
                {
                    Console.WriteLine($"批次 {currentBatch} 读取失败: {batchEx.Message}");
                    // 批量读取失败时，跳过这批节点，设置默认值
                    foreach (var nodeId in batch)
                        if (nodeMap.TryGetValue(nodeId, out var treeNode))
                            treeNode.DataType = "ReadError";
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"批量读取数据类型时出错: {ex.Message}");
        }
    }

    /// <summary>
    ///     根据当前节点，加载子节点 (原始方法，保留作为备用)
    /// </summary>
    /// <param name="opcDa"></param>
    /// <param name="currTreeEntity">当前节点</param>
    private dynamic ChildNodes(OpcUaClientHelper opcDa, OpcUaNodeTree currTreeEntity)
    {
        // 节点是否还有下级
        var childNodes = opcDa.BrowseNodeReference(currTreeEntity.NodeId);
        if (childNodes.Count <= 0) return currTreeEntity;
        currTreeEntity.Children ??= new List<OpcUaNodeTree>();
        currTreeEntity.Children.AddRange(childNodes.Select(s => new OpcUaNodeTree
        {
            NodeId = s.NodeId.ToString(),
            NodeClass = s.NodeClass,
            BrowseName = s.BrowseName.Name,
            DataType = s.NodeClass == NodeClass.Variable
                ? opcDa.ReadNode(s.NodeId.ToString()).WrappedValue.TypeInfo?.BuiltInType.ToString() ?? null
                : null
        }));
        foreach (var treeEntity in currTreeEntity.Children) ChildNodes(opcDa, treeEntity);
        return currTreeEntity;
    }

    #region Opc-Ua

    /// <summary>
    ///     扫描opcDa节点列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceVariable/opcDa/nodeSelect")]
    [HttpGet("/api/deviceVariable/opcDa/nodeSelect")]
    public async Task<dynamic> NodeSelect([FromQuery] NodeSelectByDeviceInput input)
    {
        // 网关设备
        var device = await _driver.AsSugarClient().Queryable<Device>()
            .Where(w => w.Id == input.Id)
            .Includes(w => w.DeviceConfigs)
            .FirstAsync();
        var url = device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName.ToLower() == "url")!.Value;
        return await NodeSelect(new NodeSelectInput { NodeId = input.NodeId, Url = url });
    }

    #endregion
}