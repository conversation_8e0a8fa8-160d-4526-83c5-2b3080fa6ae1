namespace IotGateway.EdgeDevice.TransPonds.SendToOpcUa.Dto;

/// <summary>
/// 分页节点查询结果
/// </summary>
public class PagedNodeResult
{
    /// <summary>
    /// 当前批次的节点列表
    /// </summary>
    public List<OpcUaNodeTree> Nodes { get; set; } = new List<OpcUaNodeTree>();

    /// <summary>
    /// 每批大小
    /// </summary>
    public int BatchSize { get; set; }

    /// <summary>
    /// 当前批次节点数量
    /// </summary>
    public int CurrentCount { get; set; }

    /// <summary>
    /// 是否还有更多数据
    /// </summary>
    public bool HasMore { get; set; }

    /// <summary>
    /// 查询耗时（毫秒）
    /// </summary>
    public double ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 连接耗时（毫秒）
    /// </summary>
    public double ConnectionMilliseconds { get; set; }

    /// <summary>
    /// 浏览耗时（毫秒）
    /// </summary>
    public double BrowseMilliseconds { get; set; }
}
