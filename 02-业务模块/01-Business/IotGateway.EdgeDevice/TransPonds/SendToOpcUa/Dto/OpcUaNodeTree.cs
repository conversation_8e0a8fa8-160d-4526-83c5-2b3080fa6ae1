using Opc.Ua;

namespace IotGateway.EdgeDevice.TransPonds.SendToOpcUa.Dto;

/// <summary>
///     OpcUa扫描节点树
/// </summary>
public class OpcUaNodeTree
{
    /// <summary>
    /// 名称
    /// </summary>
    public string BrowseName { get; set; }
    /// <summary>
    /// 节点Id
    /// </summary>
    public string NodeId { get; set; }
    /// <summary>
    /// 类型
    /// </summary>
    public NodeClass NodeClass { get; set; }
    /// <summary>
    /// 子节点列表
    /// </summary>
    public List<OpcUaNodeTree> Children { get; set; }

    /// <summary>
    /// 只有变量才有该字段
    /// </summary>
    public string DataType { get; set; }

    /// <summary>
    /// 是否为文件夹类型（Object类型为文件夹，Variable类型为节点）
    /// </summary>
    public bool IsFolder => NodeClass == NodeClass.Object;

    /// <summary>
    /// 节点类型描述
    /// </summary>
    public string NodeType => NodeClass switch
    {
        NodeClass.Object => "Folder",
        NodeClass.Variable => "Variable",
        NodeClass.Method => "Method",
        NodeClass.ObjectType => "ObjectType",
        NodeClass.VariableType => "VariableType",
        NodeClass.ReferenceType => "ReferenceType",
        NodeClass.DataType => "DataType",
        NodeClass.View => "View",
        _ => "Unknown"
    };
}

/// <summary>
///     标准规范
/// </summary>
public class OpcUaScriptValue
{
    /// <summary>
    ///     数据类型
    /// </summary>
    public string DataType { get; set; }

    /// <summary>
    ///     数据值
    /// </summary>
    public object Value { get; set; }
}