namespace IotGateway.EdgeDevice.TransPonds.SendToOpcUa.Dto;

/// <summary>
/// OpcUa连接
/// </summary>
public class NodeSelectInput
{
    /// <summary>
    ///     连接地址
    /// </summary>
    [Required]
    public string Url { get; set; }

    /// <summary>
    /// 上级节点
    /// </summary>
    public string NodeId { get; set; }

    /// <summary>
    /// 是否读取变量节点的数据类型，默认false以提升性能
    /// </summary>
    public bool ReadDataType { get; set; } = false;

    /// <summary>
    /// 使用原始浏览方法进行对比测试，默认false使用优化方法
    /// </summary>
    public bool UseOriginalBrowse { get; set; } = false;


}

/// <summary>
/// OpcUa连接
/// </summary>
public class NodeSelectByDeviceInput
{
    /// <summary>
    ///     设备Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    /// 上级节点 
    /// </summary>
    public string NodeId { get; set; }
}