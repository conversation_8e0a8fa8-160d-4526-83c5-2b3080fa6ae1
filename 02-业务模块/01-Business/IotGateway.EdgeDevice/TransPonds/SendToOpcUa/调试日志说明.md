# OPC UA 调试日志说明

## 日志输出位置

所有调试日志都通过 `Console.WriteLine` 输出，可以在以下位置查看：
- **开发环境**: Visual Studio 输出窗口或控制台
- **生产环境**: 应用程序日志或控制台输出

## 日志格式说明

所有日志都使用统一的格式：`[组件名] 日志内容`

### 主要组件标识

- `[NodeSelect]`: 主要的节点选择服务方法
- `[ConnectFast]`: 快速连接方法
- `[BrowseOptimized]`: 优化的浏览方法
- `[BrowsePaged]`: 分页浏览方法

## 完整的日志流程示例

### 正常流程日志

```
[NodeSelect] ========== 开始处理请求 ==========
[NodeSelect] URL: opc.tcp://localhost:4840
[NodeSelect] NodeId: ns=2;i=1001
[NodeSelect] EnablePaging: true
[NodeSelect] PageSize: 50
[NodeSelect] PageIndex: 0
[NodeSelect] ReadDataType: false
[NodeSelect] 创建OpcUaClientHelper成功
[NodeSelect] 开始连接服务器

[ConnectFast] 开始连接到服务器: opc.tcp://localhost:4840
[ConnectFast] 断开现有连接
[ConnectFast] 开始选择端点
[ConnectFast] 端点选择完成，耗时: 200ms，端点: opc.tcp://localhost:4840
[ConnectFast] 连接配置完成，操作超时: 10000ms
[ConnectFast] 开始创建会话
[ConnectFast] 会话创建完成，耗时: 800ms
[ConnectFast] 会话配置优化完成
[ConnectFast] 连接成功，总耗时: 1200ms

[NodeSelect] 连接成功，耗时: 1200ms
[NodeSelect] 查询节点=ns=2;i=1001，分页=true，页大小=50，页索引=0
[NodeSelect] 开始浏览节点
[NodeSelect] 使用分页浏览模式

[BrowsePaged] 开始分页浏览节点: ns=2;i=1001, 页大小: 50, 页索引: 0
[BrowsePaged] 创建NodeId成功: ns=2;i=1001
[BrowsePaged] 开始调用Session.Browse，最大节点数: 50
[BrowsePaged] Session.Browse完成，耗时: 400ms
[BrowsePaged] 验证响应成功
[BrowsePaged] 获取到结果，结果数量: 1
[BrowsePaged] 第一批引用数量: 50
[BrowsePaged] ContinuationPoint是否存在: true
[BrowsePaged] 处理第一页，直接返回
[BrowsePaged] 第一页过滤后节点数: 50

[NodeSelect] 分页浏览完成，耗时: 450ms，当前页 50 个节点，总计 50 个，还有更多: true
[NodeSelect] 开始创建节点树结构
[NodeSelect] 节点树创建完成，共 50 个节点
[NodeSelect] 节点类型统计 - Object: 30, Variable: 20, Method: 0
[NodeSelect] 跳过数据类型读取 (ReadDataType=false, 变量节点数=20)
[NodeSelect] 开始断开连接
[NodeSelect] 连接已断开
[NodeSelect] ========== 请求处理完成 ==========
[NodeSelect] 总耗时: 1800ms，成功获取 50 个子节点
[NodeSelect] 返回分页结果
```

## 问题诊断指南

### 1. 连接问题

**症状**: 长时间无响应，没有后续日志
**查看日志**:
```
[ConnectFast] 开始连接到服务器: xxx
[ConnectFast] 断开现有连接
[ConnectFast] 开始选择端点
// 如果卡在这里，说明端点选择有问题
```

**可能原因**:
- OPC UA 服务器地址错误
- 网络连接问题
- 防火墙阻止连接
- 服务器未启动

### 2. 端点选择慢

**症状**: 端点选择耗时过长（>5秒）
**查看日志**:
```
[ConnectFast] 端点选择完成，耗时: 8000ms
```

**可能原因**:
- 服务器响应慢
- 网络延迟高
- DNS解析问题

### 3. 会话创建失败

**症状**: 会话创建时出现异常
**查看日志**:
```
[ConnectFast] 开始创建会话
[ConnectFast] 连接失败，耗时: 2000ms，异常: xxx
```

**可能原因**:
- 认证失败
- 证书问题
- 服务器拒绝连接

### 4. 浏览节点慢

**症状**: 浏览操作耗时过长
**查看日志**:
```
[BrowsePaged] Session.Browse完成，耗时: 15000ms
```

**可能原因**:
- 节点数量过多
- 服务器性能问题
- 网络延迟

### 5. 没有找到节点

**症状**: 返回空结果
**查看日志**:
```
[BrowsePaged] 第一批引用数量: 0
[NodeSelect] 节点树创建完成，共 0 个节点
```

**可能原因**:
- NodeId 不存在
- 权限不足
- 节点类型过滤问题

## 性能分析

### 时间分布分析

通过日志可以分析各个步骤的耗时：

1. **连接耗时** = `[ConnectFast] 连接成功，总耗时: XXXms`
2. **浏览耗时** = `[BrowsePaged] Session.Browse完成，耗时: XXXms`
3. **数据类型读取耗时** = `[NodeSelect] 数据类型读取完成，耗时: XXXms`
4. **总耗时** = `[NodeSelect] 总耗时: XXXms`

### 性能优化建议

根据日志分析结果：

- **连接慢** (>2秒): 检查网络和服务器
- **浏览慢** (>5秒): 减少页大小或使用分页
- **数据类型读取慢** (>3秒): 设置 `ReadDataType=false`

## 常见错误模式

### 1. 超时错误
```
[ConnectFast] 连接失败，异常: The operation timed out
```
**解决**: 增加超时时间或检查网络

### 2. 节点不存在
```
[BrowsePaged] 异常: BadNodeIdUnknown
```
**解决**: 检查 NodeId 是否正确

### 3. 权限不足
```
[BrowsePaged] 异常: BadUserAccessDenied
```
**解决**: 检查用户权限配置

## 调试技巧

### 1. 逐步调试
- 先测试连接: 只看连接相关日志
- 再测试浏览: 关注浏览相关日志
- 最后测试数据类型: 启用 ReadDataType

### 2. 性能对比
- 对比分页模式和传统模式的耗时
- 对比不同页大小的性能表现
- 对比是否读取数据类型的差异

### 3. 问题定位
- 根据最后出现的日志确定卡住的位置
- 查看异常堆栈信息定位具体问题
- 对比正常和异常情况的日志差异

通过这些详细的日志，您可以精确定位 OPC UA 连接和浏览过程中的性能瓶颈和问题所在。
