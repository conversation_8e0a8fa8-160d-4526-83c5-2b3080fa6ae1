using Feng.IotGateway.Core;
using Feng.IotGateway.Core.Service.Cache;
using IotGateway.Application.Entity;
using IotGateway.Application.Exec;
using IotGateway.EdgeDevice;
using Microsoft.Extensions.Logging;
using DateTime = System.DateTime;

// ReSharper disable All

namespace IotGateway.MQTT;

/// <summary>
///     统一上报MQTT
/// </summary>
public class MasterClient : IDisposable
{
    #region 构造参数

    /// <summary>
    /// </summary>
    private CancellationTokenSource _tokenSource = new();

    /// <summary>
    /// </summary>
    private readonly IServiceScopeFactory _scopeFactory;

    /// <summary>
    ///     socket推送
    /// </summary>
    private readonly SendMessageService _send;

    /// <summary>
    ///     MQTT连接
    /// </summary>
    public IMqttClient Client { get; set; }

    /// <summary>
    ///     MQTT连接配置
    /// </summary>
    private MqttClientOptions _clientOptions;

    /// <summary>
    ///     转发配置
    /// </summary>
    public TransPond TransPond;

    /// <summary>
    ///     日志
    /// </summary>
    private readonly ILogger<MasterClient> _logger;

    /// <summary>
    ///     连接状态
    /// </summary>
    public bool IsConnected => Client?.IsConnected ?? false;

    /// <summary>
    ///     蓝卓SupOS平台
    /// </summary>
    private readonly SuperOsApplication _superOsApplication;

    /// <summary>
    ///     ELink平台
    /// </summary>
    private readonly ELinkApplication _eLinkApplication;

    /// <summary>
    ///     跟云平台
    /// </summary>
    private readonly RootCloudApplicationMessageReceived _rootCloudApplication;

    /// <summary>
    ///     记录发起同步的时间
    /// </summary>
    public DateTime DeviceSendTime;

    /// <summary>
    ///     标记,第一次同步不记录时间
    /// </summary>
    private bool _noFirstTime;

    /// <summary>
    ///     Mqtt最近运行连接记录
    /// </summary>
    /// <remarks>默认只保存 100 条</remarks>
    public Queue<RunRecordLine> Timelines { get; set; } = new();

    /// <summary>
    ///     MQTT的上下行日志记录
    /// </summary>
    /// <remarks>默认只保存 100 条</remarks>
    public Queue<RunRecordLine> RecordLines { get; set; } = new();

    /// <summary>
    ///     存储发送成功的数据
    /// </summary>
    private TaskQueue<ReportData> ReportDataQueue { get; } = new();

    /// <summary>
    ///     实时数据发送不成功保存失败的数据
    /// </summary>
    private TaskQueue<OffLine> OffLineQueue { get; } = new(100);

    /// <summary>
    ///     脚本执行
    /// </summary>
    private readonly EngineSingletonService _engine;

    /// <summary>
    ///     设备最后上报时间
    /// </summary>
    private readonly ConcurrentDictionary<long, DateTime> _lastSendTime = new();

    /// <summary>
    ///     网关设置最大离线数据,仅初始化获取一次
    /// </summary>
    private readonly int _maxOffLine;

    /// <summary>
    ///     离线数据定时器
    /// </summary>
    protected TimerX _timerPushOffLineData { get; set; }

    /// <summary>
    ///     上报数据定时器
    /// </summary>
    protected TimerX _timerAddReportData { get; set; }

    /// <summary>
    ///     离线数据队列定时器
    /// </summary>
    protected TimerX _timerOffLineQueueTask { get; set; }

    /// <summary>
    ///     同步系统时间定时器
    /// </summary>
    protected TimerX _syncSystemTime { get; set; }

    /// <summary>
    ///    心跳定时器
    /// </summary>
    protected TimerX _commonTimer { get; set; }
    /// <summary>
    ///     检查订阅状态定时器
    /// </summary>
    private TimerX _checkSubscriptionTimer;

    #endregion

    /// <summary>
    ///     MQTT订阅选项
    /// </summary>
    private MqttClientSubscribeOptions _mqttSubscribeOptions;

    /// <summary>
    ///     保存数据
    /// </summary>
    private readonly bool saveData;
    /// <summary>
    ///     订阅锁
    /// </summary>
    private readonly SemaphoreSlim _subscriptionLock = new(1, 1);
    /// <summary>
    ///     是否订阅
    /// </summary>
    private volatile bool _isSubscribed = false;
    /// <summary>
    ///     最大重试次数
    /// </summary>
    private const int MAX_RETRY_COUNT = 10; // 增加最大重试次数

    /// <summary>
    /// </summary>
    /// <param name="scopeFactory"></param>
    /// <param name="send">socket</param>
    /// <param name="logger">日志</param>
    /// <param name="superOsApplication">supOS回调</param>
    /// <param name="eLinkApplication">ELink回调</param>
    /// <param name="rootCloudApplication">树根回调</param>
    /// <param name="engine"></param>
    public MasterClient(SendMessageService send, ILogger<MasterClient> logger, SuperOsApplication superOsApplication, ELinkApplication eLinkApplication, IServiceScopeFactory scopeFactory,
        RootCloudApplicationMessageReceived rootCloudApplication, EngineSingletonService engine)
    {
        _send = send;
        _logger = logger;
        _superOsApplication = superOsApplication;
        _eLinkApplication = eLinkApplication;
        _rootCloudApplication = rootCloudApplication;
        _maxOffLine = GlobalConfigManager.GetConfigValue<int>(ConfigConst.MaxOffLine) ;
        saveData = GlobalConfigManager.GetConfigValue<bool>(ConfigConst.SaveData) ;
        _engine = engine;
        _scopeFactory = scopeFactory;
        // 解决 Mapster 映射字典变成小写的问题 
        TypeAdapterConfig.GlobalSettings.Default.NameMatchingStrategy(NameMatchingStrategy.Exact);
        try
        {
            using var scopeService = _scopeFactory.CreateScope();
            var transPondRep = scopeService.ServiceProvider.GetRequiredService<SqlSugarRepository<TransPond>>();
            TransPond = transPondRep.AsQueryable().Where(w => w.Master == true && w.Enable).Includes(w => w.TransPondTopic).First();
        }
        catch (Exception ex)
        {
            _logger.LogError($"【Master】 无法正确读取数据,Error:【{ex.Message}】");
        }

        // master mqtt的初始化
        ReConnect();

        try
        {
            using var scopeService = _scopeFactory.CreateScope();
            var offLineRep = scopeService.ServiceProvider.GetRequiredService<SqlSugarRepository<OffLine>>();
            MachineUtil.OffLineCount = offLineRep.Count(w => w.Id > 0);
        }
        catch (Exception ex)
        {
            _logger.LogError($"【缓存数据库】 无法正确读取数据,Error:【{ex.Message}】");
        }
    }

    /// <summary>
    ///     重置Master节点连接
    /// </summary>
    public void ReConnect()
    {
        // 停止master节点
        StopMaster();
        Thread.Sleep(1000);
        // 重新连接master节点
        _ = ConnectAsync();
    }

    /// <summary>
    ///     更新Master节点Topic
    /// </summary>
    public async Task ResetTopic()
    {
        if (TransPond != null)
        {
            using var scopeService = _scopeFactory.CreateScope();
            var transPondTopicRep = scopeService.ServiceProvider.GetRequiredService<SqlSugarRepository<TransPondTopic>>();
            TransPond.TransPondTopic = await transPondTopicRep.AsQueryable().Where(w => w.TransPondId == TransPond.Id).ToListAsync();
        }
    }

    #region MQTT/连接-断开

    private SendMsgEx _sendMsgEx = new SendMsgEx();

    /// <summary>
    ///     master的MQTT连接
    /// </summary>
    private async Task ConnectAsync()
    {
        try
        {
            try
            {
                if (_tokenSource.IsCancellationRequested)
                    _tokenSource = new CancellationTokenSource();
            }
            catch (Exception e)
            {
                _logger.LogError("【MQTT取消token】:" + e.Message);
            }

            // 获取转发配置
            using var scopeService = _scopeFactory.CreateScope();
            var transPondRep = scopeService.ServiceProvider.GetRequiredService<SqlSugarRepository<TransPond>>();
            TransPond = await transPondRep.CopyNew().AsQueryable().Where(w => w.Master == true && w.Enable).Includes(w => w.TransPondTopic).FirstAsync();
            if (TransPond?.MqttConfModel == null)
                return;
            var mqttConfig = TransPond.MqttConfModel;
            var mqttFactory = new MqttFactory();

            _clientOptions = new MqttClientOptionsBuilder()
                .WithClientId(mqttConfig.ClientId ?? Guid.NewGuid().ToString("N"))
                .WithTcpServer(mqttConfig.Ip, mqttConfig.Port)
                .WithCredentials(mqttConfig.UserName, mqttConfig.Password)
                .WithTimeout(TimeSpan.FromSeconds(5))
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(mqttConfig.KeepAlive))
                .WithCleanSession()
                .Build();

            #region 消息主题

            var mqttClientSubscribeOptionsBuilder = mqttFactory.CreateSubscribeOptionsBuilder();
            foreach (var topic in TransPond.TransPondTopic.Where(w => w.TransPondTopicType == TransPondTopicTypeEnum.Sub))
            {
                mqttClientSubscribeOptionsBuilder = mqttClientSubscribeOptionsBuilder.WithTopicFilter(
                    f =>
                    {
                        f.WithTopic(topic.Topic);
                        f.WithQualityOfServiceLevel(MqttQualityOfServiceLevel.AtLeastOnce);
                    });
            }

            var mqttClientSubscribeOptions = mqttClientSubscribeOptionsBuilder.Build();
            if (mqttClientSubscribeOptions.TopicFilters.Count > 0)
                _mqttSubscribeOptions = mqttClientSubscribeOptions;

            #endregion 消息主题

            Client = new MqttFactory().CreateMqttClient();
            // 消息回调
            Client.ApplicationMessageReceivedAsync += Client_ApplicationMessageReceived;
            // 连接成功
            Client.ConnectedAsync += OnConnected;
            // 连接断开
            Client.DisconnectedAsync += OnDisconnectedAsync;
            // mqtt添加到脚本
            _sendMsgEx.Connect(Client);
            _engine.Engine.SetValue("mqtt", _sendMsgEx);
            try
            {
                await Client.ConnectAsync(_clientOptions, CancellationToken.None);
            }
            catch (Exception ex)
            {
                _logger.LogError($"【MQTT】 连接错误:【{ex.Message}】");
            }
            // 增加定时同步
            _checkSubscriptionTimer = new TimerX(CheckSubscriptionStatus, null, 60_000, 60_000, "MQTT") { Async = true };

            // 设置离线存储数量>0 开启离线数据上送
            if (_maxOffLine > 0)
                _timerPushOffLineData = new TimerX(PublishOffLineTask, null, 10_000, 10_000, "Device") { Async = true };
            // 保存发送成功的数据
            _timerAddReportData = new TimerX(AddReportData, null, 10_000, 10_000, "Device") { Async = true };
            // 保存失败的数据再次存储
            _timerOffLineQueueTask = new TimerX(OffLineQueueTask, null, 10_000, 10_000, "Device") { Async = true };
            // 同步时间
            if (mqttConfig.IoTPlatformType is IoTPlatformType.ELink or IoTPlatformType.RootCloud or IoTPlatformType.SupOS)
                _syncSystemTime = new TimerX(SyncSystemTime, null, 10_000, 60 * 1000 * 30, "Device") { Async = true };

            // ELink系统属性
            if (mqttConfig.IoTPlatformType is IoTPlatformType.ELink)
                _commonTimer = new TimerX(ELinkHeartbeat, null, 10_000, 15 * 1000, "Device") { Async = true };
        }
        catch (Exception ex)
        {
            _logger.LogError("【MQTT】 初始化出错," + ex.Message);
        }
    }

    /// <summary>
    ///     MQTT断开连接事件
    /// </summary>
    [SuppressMonitor]
    private async Task OnDisconnectedAsync(MqttClientDisconnectedEventArgs args)
    {
        try
        {
            // 打印日志
            _logger.LogInformation($"MQTT断开连接,原因:【{args.Reason}】");
            // 设置订阅状态
            _isSubscribed = false;
            // 打印设置订阅状态
            _logger.LogInformation($"设置订阅状态:_isSubscribed:【{_isSubscribed}】");
            // 如果连续断开次数小于10次，则记录日志
            if (!_continuousClose)
            {
                AddToConnectRecord($"MQTT断开连接,原因:【{args.Reason}】");
                _continuousClose = true;
            }

            // 使用延迟重连而不是直接重连
            await Task.Delay(5000); // 等待5秒后重连

            // 如果客户端已经断开，则重新创建客户端
            if (!Client.IsConnected)
            {
                // 重新初始化客户端
                Client = new MqttFactory().CreateMqttClient();
                // 重新绑定事件处理
                Client.ApplicationMessageReceivedAsync += Client_ApplicationMessageReceived;
                Client.ConnectedAsync += OnConnected;
                Client.DisconnectedAsync += OnDisconnectedAsync;

                // 尝试重新连接
                try
                {
                    _logger.LogInformation("MQTT开始重新连接...");
                    await Client.ConnectAsync(_clientOptions, _tokenSource.Token);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"MQTT重连失败: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"MQTT断开事件处理发生错误: {ex.Message}");
        }
    }

    /// <summary>
    ///     连续的连接
    /// </summary>
    private bool _continuousClose;

    /// <summary>
    ///     MQTT连接成功事件
    /// </summary>
    [SuppressMonitor]
    private async Task OnConnected(MqttClientConnectedEventArgs args)
    {
        try
        {
            _continuousClose = false;
            AddToConnectRecord("MQTT连接成功");
            SendMessage("MQTT连接成功", "warning", true);

            if (TransPond == null || _mqttSubscribeOptions == null)
                return;

            // 使用信号量防止重复订阅
            await _subscriptionLock.WaitAsync();
            try
            {
                // 确保订阅
                await EnsureSubscription();
            }
            finally
            {
                _subscriptionLock.Release();
            }

            // 其他连接后的操作...
            await HandlePostConnectionTasks();
        }
        catch (Exception ex)
        {
            _logger?.LogError($"MQTT连接后处理发生错误: {ex.Message}");
            // 启动后台重试任务
            _ = Task.Run(RetrySubscriptionInBackground);
        }
    }

    /// <summary>
    ///     确保订阅
    /// </summary>
    /// <param name="ct"></param>
    /// <returns></returns>
    private async Task EnsureSubscription(CancellationToken ct = default)
    {
        // 订阅重试次数
        int retryCount = 0;
        // 订阅延迟时间
        int delayMs = 1000;

        // 循环订阅直到成功或取消
        // 打印_isSubscribed 
        _logger.LogInformation($"MQTT后台订阅重试任务，_isSubscribed:【{_isSubscribed}】");
        while (!_isSubscribed && !ct.IsCancellationRequested)
        {
            try
            {
                // 订阅
                var subResult = await Client.SubscribeAsync(_mqttSubscribeOptions).ConfigureAwait(false);

                // 订阅成功
                if (!subResult.Items.Any(a => a.ResultCode > (MqttClientSubscribeResultCode)10))
                {
                    _isSubscribed = true;
                    _logger?.LogInformation("MQTT主题订阅成功");
                    return;
                }

                // 订阅失败
                var failedTopics = subResult.Items
                    .Where(a => a.ResultCode > (MqttClientSubscribeResultCode)10)
                    .Select(a => new { Topic = a.TopicFilter.Topic, ResultCode = a.ResultCode.ToString() })
                    .ToJson();
                // 记录订阅失败
                _logger?.LogInformation($"MQTT订阅尝试 {retryCount + 1}/{MAX_RETRY_COUNT} 失败: {failedTopics}");
                // 重试次数+1
                retryCount++;
                // 达到最大重试次数，重置连接
                if (retryCount >= MAX_RETRY_COUNT)
                {
                    _logger?.LogError("MQTT订阅达到最大重试次数，准备重置连接...");
                    // 重置连接标志
                    _isSubscribed = false;
                    // 调用重连方法
                    ReConnect();
                    // 重置重试次数和延迟
                    retryCount = 0;
                    delayMs = 1000;
                    // 等待一段时间让重连完成
                    await Task.Delay(5000, ct);
                }
                else
                {
                    // 指数退避策略
                    delayMs = Math.Min(delayMs * 2, 30000); // 最大延迟30秒
                    // 延迟
                    await Task.Delay(delayMs, ct);
                }
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                // 记录订阅异常
                _logger?.LogError($"MQTT订阅发生异常: {ex.Message}");
                // 延迟
                await Task.Delay(delayMs, ct);
            }
        }
        // 打印_isSubscribed 
        _logger?.LogInformation($"MQTT后台订阅重试任务，_isSubscribed:【{_isSubscribed}】");
    }

    /// <summary>
    ///     后台订阅重试任务
    /// </summary>
    private async Task RetrySubscriptionInBackground()
    {
        using var cts = new CancellationTokenSource();
        try
        {
            // 打印_isSubscribed 
            _logger.LogInformation($"MQTT后台订阅重试任务，_isSubscribed:【{_isSubscribed}】");
            while (!_isSubscribed)
            {
                // 打印_isSubscribed 
                _logger.LogInformation($"MQTT后台订阅重试任务，_isSubscribed:【{_isSubscribed}】");
                // 如果MQTT连接断开，等待重新连接
                if (!Client.IsConnected)
                {
                    await Task.Delay(5000); // 等待重新连接
                    // 打印Client.IsConnected 
                    _logger.LogInformation($"MQTT后台订阅重试任务，Client.IsConnected:【{Client.IsConnected}】");
                    continue;
                }

                // 订阅
                await EnsureSubscription(cts.Token);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError($"后台订阅重试任务发生错误: {ex.Message}");
        }
    }

    /// <summary>
    ///     连接后处理任务
    /// </summary>
    /// <returns></returns>
    private async Task HandlePostConnectionTasks()
    {
        // 如果MQTT连接断开或者平台不是SupOS，则不处理
        if (!Client.IsConnected || TransPond?.MqttConfModel?.IoTPlatformType != IoTPlatformType.SupOS)
            return;

        // 将任务添加到队列
        await TaskQueued.EnqueueAsync(async (provider, token) =>
        {
            try
            {
                using var scopeService = _scopeFactory.CreateScope();
                var deviceRepo = scopeService.ServiceProvider.GetRequiredService<SqlSugarRepository<Device>>();
                var deviceList = await deviceRepo.CopyNew().AsQueryable()
                    .Where(w => w.Enable)
                    .Includes(c => c.DeviceVariable.Where(w => w.Enable).ToList())
                    .ToListAsync();

                await PublishMeta(deviceList);
            }
            catch (Exception e)
            {
                _logger?.LogError($"【SupOS】上报元数据失败: {e.Message}");
            }
        });
    }

    #endregion

    #region 发送元数据

    /// <summary>
    ///     发送网关测设备采集点配置消息
    /// </summary>
    /// <param name="deviceList"></param>
    /// <param name="transPondTopicPurpose">默认直接元数据上报</param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public async Task PublishMeta(List<Device> deviceList, TransPondTopicPurposeEnum transPondTopicPurpose = TransPondTopicPurposeEnum.Meta)
    {
        try
        {
            if (TransPond == null)
                return;
            var metaVariable = new List<MetaVariable>();
            foreach (var device in deviceList)
                foreach (var variable in device.DeviceVariable.Where(w => w.Enable && w.SendType != SendTypeEnum.Never).ToList())
                    try
                    {
                        metaVariable.Add(new MetaVariable
                        {
                            Description = variable.Description,
                            Identifier = TransPond.Identifier + "/" + device.DeviceName + "/" + variable.Identifier,
                            Name = variable.Name,
                            ProtectType = variable.DeviceVariableEx.ProtectType,
                            TransitionType = variable.TransitionType,
                            Unit = variable.Unit
                        });
                    }
                    catch (Exception e)
                    {
                        // 避免有脏数据影响整个遍历
                        _logger.LogError($"解析设备属性出现问题,Error:{e.Message},StackTrace:【{e.StackTrace}】,variable:【{JSON.Serialize(variable)}】");
                    }

            // slave上报元数据
            await MessageCenter.PublishAsync(EventConst.DeviceMeta, metaVariable);
            // 根据版本号决定使用哪个主题
            var topicName = "";
            if (transPondTopicPurpose == TransPondTopicPurposeEnum.Meta)
            {
                if (MachineUtil.ServerVersion != null && MachineUtil.ServerVersion?.Minor >= 1)
                    topicName = "metatag/report";
                else
                    topicName = "metatag/retain";
            }
            else
            {
                if (MachineUtil.ServerVersion != null && MachineUtil.ServerVersion?.Minor >= 1)
                    topicName = "metanotify/push_reply";
                else
                    topicName = "metatag/push_reply";
            }

            var metaTopic = TransPond.TransPondTopic.FirstOrDefault(f => f.TransPondTopicPurpose == (transPondTopicPurpose == TransPondTopicPurposeEnum.Meta
                ? TransPondTopicPurposeEnum.Meta
                : TransPondTopicPurposeEnum.MetaPushReply) && f.Topic.Contains(topicName));
            if (metaTopic == null)
                return;

            if (!string.IsNullOrEmpty(metaTopic.Config))
            {
                lock (this)
                {
                    _engine.Engine.SetValue("payload", metaVariable);
                    var execValue = _engine.Engine.Evaluate(metaTopic.Config ?? "", new ScriptParsingOptions { Tolerant = true, AllowReturnOutsideFunction = true }).ToObject();
                    if (execValue != null)
                    {
                        if (TransPond.MqttConfModel?.IoTPlatformType == IoTPlatformType.SupOS)
                            metaVariable = execValue.Adapt<List<MetaVariable>>();
                        else
                        {
                            try
                            {
                                // 数据上送
                                var pub = Client.PublishAsync(new MqttApplicationMessageBuilder()
                                    .WithTopic(metaTopic.Topic)
                                    .WithPayload(Encoding.UTF8.GetBytes(execValue.ToString()))
                                    .WithQualityOfServiceLevel(metaTopic.Qos).Build()).GetAwaiter().GetResult();
                                _logger.LogInformation($"【MQTT】 上报元数据");
                            }
                            catch
                            {
                            }
                        }
                    }
                }
            }
            // 
            switch (TransPond.MqttConfModel?.IoTPlatformType)
            {
                case IoTPlatformType.SupOS:
                    {
                        // 使用新版本
                        if (MachineUtil.ServerVersion != null && MachineUtil.ServerVersion.Minor >= 1)
                            await _superOsApplication.MetaNew(metaVariable, Client, metaTopic);
                        else
                            await _superOsApplication.Meta(metaVariable, Client, metaTopic);
                        break;
                    }
                default:
                    Client.PublishAsync(new MqttApplicationMessageBuilder()
                        .WithTopic(metaTopic.Topic)
                        .WithPayload(Encoding.UTF8.GetBytes(metaVariable.ToJson()))
                        .WithQualityOfServiceLevel(metaTopic.Qos).Build()).GetAwaiter().GetResult();
                    _logger.LogInformation("【MQTT】 上报元数据");
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"上报采集属性配置 Error:{ex.Message}");
        }
    }

    /// <summary>
    ///     删除元数据
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="deviceVariableList"></param>
    public async Task DeleteMeta(string deviceName, List<DeviceVariable> deviceVariableList)
    {
        // 获取元数据主题
        var metaTopic = TransPond.TransPondTopic.FirstOrDefault(f => f.TransPondTopicPurpose == TransPondTopicPurposeEnum.Meta
            && f.Topic.Contains("metatag/report"));
        if (metaTopic == null)
            return;

        var metaVariable = new List<MetaVariable>();
        foreach (var variable in deviceVariableList.Where(w => w.Enable && w.SendType != SendTypeEnum.Never).ToList())
            try
            {
                metaVariable.Add(new MetaVariable
                {
                    Description = variable.Description,
                    Identifier = TransPond.Identifier + "/" + deviceName + "/" + variable.Identifier,
                    Name = variable.Name,
                    ProtectType = variable.DeviceVariableEx.ProtectType,
                    TransitionType = variable.TransitionType,
                    Unit = variable.Unit
                });
            }
            catch (Exception e)
            {
                // 避免有脏数据影响整个遍历
                _logger.LogError($"解析设备属性出现问题,Error:{e.Message},StackTrace:【{e.StackTrace}】,variable:【{JSON.Serialize(variable)}】");
            }

        await _superOsApplication.MetaDelete(metaVariable, Client, metaTopic);
    }
    #endregion

    #region 实时数据发送

    /// <summary>
    ///     将采集数据解析成不同平台需要的转发格式数据
    /// </summary>
    /// <param name="input"></param>
    /// <param name="transPondTopicPurpose">默认走实时通道发送数据</param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task PublishAsync(PayLoad input, TransPondTopicPurposeEnum transPondTopicPurpose = TransPondTopicPurposeEnum.OnLine)
    {
        try
        {
            if (TransPond == null)
                return;
            if (TransPond?.MqttConfModel == null)
            {
                await _send.Send("MQTT未配置,数据丢弃", input.DeviceName + "Resp");
                return;
            }

            // 根据发送类型过滤数据
            try
            {
                input.Values = await FilterValuesByTransPondSendType(input);
            }
            catch (Exception ex)
            {
                var filterErrorMsg = $@"设备:【{input.DeviceName}】数据过滤失败
错误类型: {ex.GetType().Name}
错误信息: {ex.Message}
堆栈信息: {ex.StackTrace}
输入数据: {JSON.Serialize(input)}";
                _logger.LogError(filterErrorMsg);
                SendMessage($"设备:【{input.DeviceName}】数据过滤失败,详细错误已记录到日志", "error", true);
                return;
            }

            var pushVariable = new VariableSendToPlatform
            {
                ParentTime = input.Ts,
                DeviceName = input.DeviceName,
                DriverName = input.DriverName,
                Params = new Dictionary<string, TransPondParamValue>()
            };

            try
            {
                foreach (var (identifier, value) in input.Values)
                {
                    var newIdentifier = identifier;
                    // supOS默认格式
                    if (TransPond?.MqttConfModel?.IoTPlatformType == IoTPlatformType.SupOS)
                        newIdentifier = TransPond.Identifier + "/" + input.DeviceName + "/" + identifier;

                    pushVariable.Params.Add(newIdentifier, new TransPondParamValue
                    {
                        Value = value.Value,
                        Time = value.ReadTime,
                        DataType = value.TransitionType
                    });
                }
            }
            catch (Exception ex)
            {
                var parseErrorMsg = $@"设备:【{input.DeviceName}】解析实时数据失败
错误类型: {ex.GetType().Name}
错误信息: {ex.Message}
堆栈信息: {ex.StackTrace}
输入数据: {JSON.Serialize(input)}
当前参数: {JSON.Serialize(pushVariable)}";
                _logger.LogError(parseErrorMsg);
                SendMessage($"设备:【{input.DeviceName}】解析实时数据失败,详细错误已记录到日志", "error", true);
                return;
            }

            if (pushVariable.Params.Any())
            {
                await PublishOnLineAsync(pushVariable, transPondTopicPurpose);
            }
        }
        catch (Exception ex)
        {
            var detailMessage = $@"设备:【{input.DeviceName}】推送实时数据失败
错误类型: {ex.GetType().Name}
错误信息: {ex.Message}
堆栈信息: {ex.StackTrace}
输入数据: {JSON.Serialize(input)}
MQTT状态: {(Client?.IsConnected ?? false)}
TransPond配置: {JSON.Serialize(TransPond?.MqttConfModel)}";

            _logger.LogError(detailMessage);
            SendMessage($"设备:【{input.DeviceName}】推送实时数据失败,详细错误已记录到日志", "error", true);
        }
    }

    /// <summary>
    ///     新增辅助方法处理数据过滤逻辑
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task<Dictionary<string, ParamValue>> FilterValuesByTransPondSendType(PayLoad input)
    {
        switch (TransPond.MqttConfModel?.SendType)
        {
            case TransPondSendTypeEnum.Always:
                return input.Values.Where(i => i.Value.Value != null)
                    .ToDictionary(i => i.Key, i => i.Value);

            case TransPondSendTypeEnum.PubPeriod: // 周期上报
                var nowTime = Common.Extension.DateTime.Now();
                // 上次发送时间
                if (_lastSendTime.TryGetValue(input.DeviceId, out DateTime value))
                {
                    // 设置周期时间
                    var pubPeriod = GetPubPeriodInMilliseconds(TransPond.MqttConfModel);
                    // 当前时间-上次发送时间 < 间隔时间
                    if ((nowTime - value).TotalMilliseconds < pubPeriod)
                        return new Dictionary<string, ParamValue>();
                    _lastSendTime[input.DeviceId] = nowTime;
                }
                else
                {
                    _lastSendTime.TryAdd(input.DeviceId, Common.Extension.DateTime.Now());
                }
                return input.Values.Where(i => i.Value.Value != null)
                    .ToDictionary(i => i.Key, i => i.Value);

            case TransPondSendTypeEnum.Changed:
                return input.Values.Where(i =>
                    (i.Value.Value != null && i.Value.CookieValue?.ToString() != i.Value.Value) ||
                    (i.Value.CookieValue == null && i.Value.Value != null))
                    .ToDictionary(i => i.Key, i => i.Value);

            default:
                return input.Values.Where(i => i.Value.Value != null)
                    .ToDictionary(i => i.Key, i => i.Value);
        }
    }

    /// <summary>
    ///     获取发布周期
    /// </summary>
    /// <param name="config"></param>
    /// <returns></returns>
    private int GetPubPeriodInMilliseconds(MqttConfModel config)
    {
        return config.PubPeriodUnit switch
        {
            "毫秒" => config.PubPeriod,
            "秒" => config.PubPeriod * 1000,
            "分钟" => config.PubPeriod * 1000 * 60,
            _ => config.PubPeriod * 1000 * 60 * 60 // 小时
        };
    }

    /// <summary>
    ///     发送实时采集消息
    /// </summary>
    /// <param name="input"></param>
    /// <param name="transPondTopicPurpose">默认走实时通道发送数据</param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    private async Task PublishOnLineAsync(VariableSendToPlatform input, TransPondTopicPurposeEnum transPondTopicPurpose = TransPondTopicPurposeEnum.OnLine)
    {
        try
        {
            // 验证必要条件
            if (TransPond == null || TransPond.TransPondTopic == null)
                return;

            // 获取在线主题配置
            var onLine = TransPond.TransPondTopic.FirstOrDefault(f => f.TransPondTopicPurpose == (transPondTopicPurpose == TransPondTopicPurposeEnum.OnLine
                ? TransPondTopicPurposeEnum.OnLine
                : TransPondTopicPurposeEnum.CloudRequestRefreshRealTimeDeviceDataReply));
            if (onLine == null)
                return;

            var onLineTopic = onLine.Topic;
            MqttClientPublishResult onLineSuccess = null;

            // 安全地计算时间
            DateTime readTime;
            try
            {
                // 检查时间戳的长度，如果超过13位需要转换
                var timestamp = input.ParentTime.ToString();
                if (timestamp.Length > 13)
                {
                    // 转换为13位时间戳
                    timestamp = timestamp.Substring(0, 13);
                    input.ParentTime = long.Parse(timestamp);
                }
                readTime = Common.Extension.DateTime.ToTime(input.ParentTime);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($@"时间戳转换失败，使用当前时间
                        错误类型: {ex.GetType().Name}
                        错误信息: {ex.Message}
                        时间戳值: {input.ParentTime}");
                readTime = Common.Extension.DateTime.Now();
            }

            try
            {
                // 检查MQTT连接
                MqttConnect();

                // 处理动态主题
                if (onLine.Rule == TransPondTopicRuleEnum.Dynamic)
                    onLineTopic = DynamicRuleTopic(onLineTopic, input);

                // 根据平台类型发送数据
                onLineSuccess = await PublishByPlatformType(input, onLine, onLineTopic);
                // 处理发送结果
                if (onLineSuccess?.ReasonCode == MqttClientPublishReasonCode.Success)
                {
                    await HandleSuccessfulPublish(input, onLineTopic, readTime);
                }
                else
                {
                    await HandleFailedPublish(input, onLineTopic, onLineSuccess);
                }
            }
            catch (Exception ex)
            {
                var publishErrorMsg = $@"设备:【{input.DeviceName}】发送数据失败
错误类型: {ex.GetType().Name}
错误信息: {ex.Message}
堆栈信息: {ex.StackTrace}
Topic: {onLineTopic}
平台类型: {TransPond?.MqttConfModel?.IoTPlatformType}
数据内容: {JSON.Serialize(input)}
时间戳值: {input.ParentTime}
转换后时间: {readTime}";
                _logger.LogError(publishErrorMsg);
                throw Oops.Oh("发送数据失败：" + publishErrorMsg);
            }
        }
        catch (Exception ex)
        {
            var detailMessage = $@"设备:【{input.DeviceName}】Master MQTT 推送实时数据失败
错误类型: {ex.GetType().Name} 
错误信息: {ex.Message}
堆栈信息: {ex.StackTrace}
数据内容: {JSON.Serialize(input)}
MQTT连接状态: {(Client?.IsConnected ?? false)}
TransPond配置: {JSON.Serialize(TransPond?.MqttConfModel)}";

            _logger.LogError(detailMessage);
            SendMessage($"设备:【{input.DeviceName}】推送实时数据失败,详细错误已记录到日志", "error", true);
        }
    }

    // 新增辅助方法处理平台类型发送
    private async Task<MqttClientPublishResult> PublishByPlatformType(VariableSendToPlatform input, TransPondTopic onLine, string onLineTopic)
    {
        switch (TransPond?.MqttConfModel?.IoTPlatformType)
        {
            case IoTPlatformType.SupOS:
                if (onLine.Config is null or "")
                    return await _superOsApplication.PublishSupOsAsync(input, onLine, Client, TransPond);

                lock (this)
                {
                    _engine.Engine.SetValue("payload", input);
                    var execValue = _engine.Engine.Evaluate(onLine.Config ?? "", new ScriptParsingOptions { Tolerant = true, AllowReturnOutsideFunction = true }).ToObject();
                    if (execValue != null)
                    {
                        input = execValue.Adapt<VariableSendToPlatform>();
                        return _superOsApplication.PublishSupOsAsync(input, onLine, Client, TransPond).GetAwaiter().GetResult();
                    }
                }
                break;

            case IoTPlatformType.RootCloud:
                return await _rootCloudApplication.PublishRootCloudAsync(input, onLine, Client, TransPond);

            case IoTPlatformType.ELink:
                return await _eLinkApplication.PublishAsync(JSON.Serialize(input), onLine, Client);

            case IoTPlatformType.MQTT:
            case IoTPlatformType.IotSuite:
                try
                {
                    if (string.IsNullOrEmpty(onLine.Config))
                    {
                        return await _eLinkApplication.PublishAsync(JSON.Serialize(input), onLine, Client);
                    }
                    else
                    {
                        lock (this)
                        {
                            _engine.Engine.SetValue("payload", input);
                            _engine.Engine.SetValue("time", Common.Extension.DateTime.ShangHai());
                            var getValue = _engine.Engine.Evaluate(onLine.Config, new ScriptParsingOptions { Tolerant = true, AllowReturnOutsideFunction = true }).ToObject();
                            if (getValue != null)
                            {
                                return _eLinkApplication.PublishAsync(getValue.ToString()!, onLine, Client!).GetAwaiter().GetResult();
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    var scriptErrorMsg = $@"设备:【{input.DeviceName}】脚本执行失败
错误类型: {ex.GetType().Name}
错误信息: {ex.Message}
堆栈信息: {ex.StackTrace}
Topic: {onLineTopic}
脚本内容: {onLine.Config}";
                    _logger.LogError(scriptErrorMsg);
                    throw Oops.Oh("脚本执行失败：" + scriptErrorMsg);
                }
                break;
            default:
                throw Oops.Oh("平台类型不支持");
        }

        return null;
    }

    // 新增辅助方法处理发送成功
    private async Task HandleSuccessfulPublish(VariableSendToPlatform input, string onLineTopic, DateTime readTime)
    {
        if (saveData)
        {
            ReportDataQueue.Add(new ReportData
            {
                Id = YitIdHelper.NextId(),
                Data = JSON.Serialize(input.Params),
                DeviceName = input.DeviceName,
                ReadTime = readTime,
                ReportDataType = ReportDataTypeEnum.OnLine,
                Time = readTime.ToString("MM-dd-HH")
            });
        }

        SendMessage($"设备:【{input.DeviceName}】,MQTT推送实时数据成功,Topic:【{onLineTopic}】", "");
        MachineUtil.SendCount++;
    }

    // 新增辅助方法处理发送失败
    private async Task HandleFailedPublish(VariableSendToPlatform input, string onLineTopic, MqttClientPublishResult onLineSuccess)
    {
        if (onLineSuccess != null)
        {
            SendMessage($"设备:【{input.DeviceName}】,MQTT送实时数据失败,上送Topic:【{onLineTopic}】,失败原因:【{onLineSuccess.ReasonString}】", "error", true);
        }

        try
        {
            if (_maxOffLine != 0)
            {
                var offLine = new OffLine
                {
                    Id = YitIdHelper.NextId(),
                    Params = Convert.ToBase64String(JSON.Serialize(input).Compress()),
                    DeviceName = input.DeviceName,
                    TimeStamp = input.ParentTime
                };

                MachineUtil.OffLineCount++;
                OffLineQueue.Add(offLine);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($@"【存储发送失败实时数据】失败
错误类型: {ex.GetType().Name}
错误信息: {ex.Message}
堆栈信息: {ex.StackTrace}
设备名称: {input.DeviceName}");
        }

        SendMessage($"设备:【{input.DeviceName}】,Master MQTT 连接状态:【{Client.IsConnected}】,已本地存储,已存储数量:【{MachineUtil.OffLineCount}】条！", "debug", true);
    }

    /// <summary>
    ///     生成动态topic
    /// </summary>
    /// <param name="onLineTopic"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    private string DynamicRuleTopic(string onLineTopic, VariableSendToPlatform input)
    {
        // 设备名称
        if (onLineTopic.Contains("${deviceName}"))
            onLineTopic = onLineTopic.Replace("${deviceName}", input.DeviceName);
        // 转发标识符
        if (onLineTopic.Contains("${transPondId}"))
            onLineTopic = onLineTopic.Replace("${transPondId}", TransPond.Identifier);
        // 协议名称
        if (onLineTopic.Contains("${driverName}"))
            onLineTopic = onLineTopic.Replace("${driverName}", input.DriverName);
        return onLineTopic;
    }

    #endregion

    #region 离线数据发送

    /// <summary>
    ///     发送离线采集信息
    /// </summary>
    private async Task PublishOffLineTask(Object state)
    {
        try
        {
            if (Client is { IsConnected: true } && TransPond != null && !_tokenSource.IsCancellationRequested)
            {
                using var scopeService = _scopeFactory.CreateScope();
                var offLineRep = scopeService.ServiceProvider.GetRequiredService<SqlSugarRepository<OffLine>>();
                List<OffLine> offLineData = new();
                // 异常计数,重置
                if (MachineUtil.OffLineCount <= 0)
                {
                    MachineUtil.OffLineCount = offLineData.Count;
                    await Task.Delay(3000, _tokenSource.Token);
                }

                var delOffLineList = new List<OffLine>();
                var offLineConfig = TransPond.TransPondTopic.FirstOrDefault(f => f.TransPondTopicPurpose == TransPondTopicPurposeEnum.OffLine);
                if (offLineConfig == null)
                    return;

                switch (TransPond.MqttConfModel?.OffLinePubSpeedType)
                {
                    case OffLinePubSpeedTypeEnum.Fast:
                        {
                            // 每次处理500条数据,每次sql读取50条
                            await offLineRep.CopyNew().AsQueryable().ForEachByPageAsync(w => { offLineData.Add(w); }, 1, 200, 50);

                            async void Body(OffLine offLine, ParallelLoopState state)
                            {
                                try
                                {
                                    if (state.IsStopped || _tokenSource.IsCancellationRequested) return;
                                    // 解密
                                    offLine.Params = Convert.FromBase64String(offLine.Params).Decompress();
                                    // 
                                    var isSuccess = PublishOffLine(offLine.Params, offLineConfig)
                                        .GetAwaiter()
                                        .GetResult();
                                    if (isSuccess == PublishOffLineEnum.Bad) return;

                                    delOffLineList.Add(offLine);
                                    if (Interlocked.Decrement(ref MachineUtil.OffLineCount) <= 0)
                                    {
                                        state.Stop();
                                        return;
                                    }

                                    if (isSuccess != PublishOffLineEnum.Success) return;
                                    await ReportDataQueueAdd(offLine);

                                    SendMessage($"设备:【{offLine.DeviceName}】,MQTT推送离线数据成功,剩余数量:【{MachineUtil.OffLineCount}】", "debug");
                                }
                                catch (Exception e)
                                {
                                    SendMessage($"设备:【{offLine.DeviceName}】,MQTT推送离线数据失败,Error:{e.Message}", "error");
                                }
                            }

                            Parallel.ForEach(offLineData, Body);
                        }
                        break;
                    case OffLinePubSpeedTypeEnum.Moderate:
                    case OffLinePubSpeedTypeEnum.Slow:
                        {
                            // 每次处理50条数据,每次sql读取25条
                            await offLineRep.CopyNew().AsQueryable().ForEachByPageAsync(w => { offLineData.Add(w); }, 1, 50, 25);
                            foreach (var offLine in offLineData)
                            {
                                if (_tokenSource.IsCancellationRequested) continue;
                                // 解密
                                offLine.Params = Convert.FromBase64String(offLine.Params).Decompress();
                                var isSuccess = PublishOffLine(offLine.Params, offLineConfig)
                                    .GetAwaiter()
                                    .GetResult();
                                if (isSuccess == PublishOffLineEnum.Bad) continue;

                                delOffLineList.Add(offLine);
                                if (Interlocked.Decrement(ref MachineUtil.OffLineCount) <= 0)
                                    continue;
                                if (isSuccess != PublishOffLineEnum.Success) continue;
                                await ReportDataQueueAdd(offLine);

                                SendMessage($"设备:【{offLine.DeviceName}】,MQTT推送离线数据成功,剩余数量:【{MachineUtil.OffLineCount}】", "debug");
                                if (TransPond.MqttConfModel?.OffLinePubSpeedType == OffLinePubSpeedTypeEnum.Slow)
                                    await Task.Delay(100, _tokenSource.Token);
                            }
                        }
                        break;
                    default:
                        _logger.LogError("转发未配置【离线数据上报速率】");
                        break;
                }

                // 批量删除离线数据
                await offLineRep.CopyNew().DeleteAsync(delOffLineList);
            }
            else
            {
                if (!_tokenSource.IsCancellationRequested)
                    await Task.Delay(1000 * 10, _tokenSource.Token);
            }
        }
        catch (Exception ex)
        {
            if (_tokenSource.IsCancellationRequested)
                return;
            _logger.LogError($"【发送离线采集信息】Error:【{ex.Message}】");
        }

        if (MachineUtil.OffLineCount > 0)
        {
            // 还有缓存数据，则立刻执行下一次
            _timerPushOffLineData.SetNext(0);
        }
    }

    /// <summary>
    ///     发送离线数据到平台
    /// </summary>
    /// <param name="offLineMessage"></param>
    /// <param name="offLine">离线topic配置</param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    private async Task<PublishOffLineEnum> PublishOffLine(string offLineMessage, TransPondTopic offLine)
    {
        try
        {
            var data = JSON.Deserialize<VariableSendToPlatform>(offLineMessage);
            if (!data.Params.Any())
                return PublishOffLineEnum.Delete;
            MqttClientPublishResult onLineSuccess = null;
            var offLineTopic = offLine.Topic;
            try
            {
                switch (TransPond?.MqttConfModel?.IoTPlatformType)
                {
                    case IoTPlatformType.ELink:
                        {
                            onLineSuccess = await _eLinkApplication.PublishAsync(offLineMessage, offLine, Client);
                            break;
                        }
                    case IoTPlatformType.SupOS:
                        onLineSuccess = await _superOsApplication.PublishSupOsAsync(data, offLine, Client, TransPond);
                        break;
                    case IoTPlatformType.RootCloud:
                        onLineSuccess = await _rootCloudApplication.PublishRootCloudAsync(data, offLine, Client, TransPond);
                        break;
                    case IoTPlatformType.MQTT:
                        {
                            lock (this)
                            {
                                try
                                {
                                    if (offLine.Rule == TransPondTopicRuleEnum.Dynamic)
                                    {
                                        // 设备名称
                                        if (offLineTopic.Contains("${deviceName}"))
                                            offLineTopic = offLineTopic.Replace("${deviceName}", data.DeviceName);
                                        // 转发标识符
                                        if (offLineTopic.Contains("${transPondId}"))
                                            offLineTopic = offLineTopic.Replace("${transPondId}", TransPond.Identifier);
                                        // 协议名称
                                        if (offLineTopic.Contains("${driverName}"))
                                            offLineTopic = offLineTopic.Replace("${driverName}", data.DriverName);
                                    }

                                    _engine.Engine.SetValue("payload", data);
                                    var getValue = _engine.Engine.Evaluate(offLine.Config, new ScriptParsingOptions { Tolerant = true, AllowReturnOutsideFunction = true }).ToObject();
                                    if (getValue != null)
                                        onLineSuccess = _eLinkApplication.PublishAsync(getValue.ToString(), offLine, Client).GetAwaiter().GetResult();
                                }
                                catch (Exception e)
                                {
                                    SendMessage($"设备:【{data.DeviceName}】,【MQTT】,上送Topic:【{offLine.Topic}】,失败原因:【{e.Message}】", "error", true);
                                }
                            }

                            break;
                        }
                    case IoTPlatformType.IotSuite:
                        {
                            // 发送配置Topic
                            lock (this)
                            {
                                try
                                {
                                    _engine.Engine.SetValue("payload", data);
                                    _engine.Engine.SetValue("time", Common.Extension.DateTime.Now());
                                    var getValue = _engine.Engine.Evaluate(offLine.Config, new ScriptParsingOptions { Tolerant = true, AllowReturnOutsideFunction = true })
                                        .ToObject();
                                    if (getValue != null)
                                        onLineSuccess = _eLinkApplication.PublishAsync(getValue.ToString(), offLine, Client).GetAwaiter().GetResult();
                                }
                                catch (Exception e)
                                {
                                    SendMessage($"设备:【{data.DeviceName}】,【IotSuite】,上送Topic:【{offLine.Topic}】,失败原因:【{e.Message}】", "error", true);
                                    return PublishOffLineEnum.Bad;
                                }
                            }

                            break;
                        }
                    default:
                        return PublishOffLineEnum.Delete;
                }
            }
            catch (Exception e)
            {
                SendMessage($"设备:【{data.DeviceName}】,离线数据推送失败,上送Topic:【{offLineTopic}】,失败原因:【{e.Message}】", "error", true);
                return PublishOffLineEnum.Bad;
            }

            if (onLineSuccess == null) return PublishOffLineEnum.Bad;
            if (onLineSuccess.ReasonCode == MqttClientPublishReasonCode.Success) return PublishOffLineEnum.Success;
            SendMessage($"设备:【{data.DeviceName}】,MQTT推送离线数据失败,上送Topic:【{offLineTopic}】,失败原因:【{onLineSuccess.ReasonString}】", "error", true);
            return PublishOffLineEnum.Bad;
        }
        catch (Exception ex)
        {
            _logger.LogError($"MQTT OffLine Publish Error:{ex.Message}");
            return PublishOffLineEnum.Bad;
        }
    }

    #endregion 离线数据发送

    /// <summary>
    ///     消息回调
    /// </summary>
    /// <param name="e"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    private async Task Client_ApplicationMessageReceived(MqttApplicationMessageReceivedEventArgs e)
    {
        try
        {
            // 获取topic
            var topic = e.ApplicationMessage.Topic;
            // 获取qos
            var level = e.ApplicationMessage.QualityOfServiceLevel;
            // 获取配置
            var getTopic = TransPond?.TransPondTopic.FirstOrDefault(f => f.Topic == topic);
            if (getTopic == null)
            {
                _logger.LogError($"MQTT回调，Topic未配置:【{topic}】");
                return;
            }

            // 根据平台类型进行处理
            switch (TransPond?.MqttConfModel?.IoTPlatformType)
            {
                case IoTPlatformType.ELink:
                    {
                        // 获取payload
                        var payLoad = e.ApplicationMessage.ConvertPayloadToString();
                        _logger.LogInformation($"MQTT回调，【ELink】 Topic:【{topic}】,Level:【{level}】 Payload:【{payLoad}】");
                        AddRecord("【ELink】回调", topic, level, payLoad);

                        switch (getTopic.TransPondTopicPurpose)
                        {
                            case TransPondTopicPurposeEnum.Variable:
                                _logger.LogInformation($"MQTT回调，【ELink】 收到属性下写");
                                // 接收写入
                                await _eLinkApplication.ReceiveWrite(e);
                                break;
                            case TransPondTopicPurposeEnum.Share:
                                // 发送共享数据
                                await MessageCenter.PublishAsync(EventConst.WriteShare, payLoad);
                                break;
                            case TransPondTopicPurposeEnum.SysTimeSub:
                                // 接收时间同步
                                await _eLinkApplication.ReceiveSysTime(payLoad);
                                break;
                            case TransPondTopicPurposeEnum.Cmd:
                                // 接收命令
                                await _eLinkApplication.Cmd(payLoad);
                                break;
                            case TransPondTopicPurposeEnum.DeviceCmd:
                                // 接收设备命令
                                await _eLinkApplication.DeviceCmd(payLoad);
                                break;
                            case TransPondTopicPurposeEnum.SubOta:
                                // 接收OTA命令
                                await _eLinkApplication.DeviceCmd(payLoad);
                                break;
                        }

                        break;
                    }
                case IoTPlatformType.SupOS:
                    {
                        switch (getTopic.TransPondTopicPurpose)
                        {
                            case TransPondTopicPurposeEnum.Variable:
                                {
                                    var writeModel = ValueSequence.Parser.ParseFrom(e.ApplicationMessage.PayloadSegment.ToArray());
                                    _logger.LogInformation($"【SupOS下写】 Topic:【{topic}】,Level:【{level}】 Payload:【{JSON.Serialize(writeModel)}】");
                                    AddRecord("【SupOS】下写", topic, level, JSON.Serialize(writeModel));
                                    await _superOsApplication.ChangeToDeviceWriteRequest(writeModel);
                                }
                                break;
                            case TransPondTopicPurposeEnum.MetaPush:
                                {
                                    AddRecord("【SupOS】上送设备属性", topic, level, "");
                                    _logger.LogInformation($"【SupOS上送设备属性】 Topic:【{topic}】,Level:【{level}】");
                                    using var scopeService = _scopeFactory.CreateScope();
                                    var deviceRepo = scopeService.ServiceProvider.GetRequiredService<SqlSugarRepository<Device>>();
                                    var deviceList = await deviceRepo.AsQueryable().Where(w => w.Enable)
                                        .Includes(c => c.DeviceVariable.Where(w => w.Enable).ToList())
                                        .ToListAsync();
                                    await PublishMeta(deviceList, TransPondTopicPurposeEnum.MetaPushReply);
                                }
                                break;
                            case TransPondTopicPurposeEnum.SysTimeSub:
                                {
                                    var downTextByTopic = RtdEvent.Parser.ParseFrom(e.ApplicationMessage.PayloadSegment.ToArray());
                                    _logger.LogInformation($"【SupOS时间同步】Topic:【{topic}】,Level:【{level}】 Payload:【{JSON.Serialize(downTextByTopic)}】");
                                    AddRecord("【SupOS】时间同步", topic, level, JSON.Serialize(downTextByTopic));
                                    if (downTextByTopic == null) return;
                                    var rtdEvent = ServerCoordinate.Parser.ParseFrom(downTextByTopic.Payload);
                                    // 持久化缓存版本号
                                    if (rtdEvent.ServerVersion != null)
                                    {
                                        // 将服务器版本号持久化到MachineUtil中
                                        MachineUtil.ServerVersion = new ProtocolVersionInfo
                                        {
                                            Major = rtdEvent.ServerVersion.Major,
                                            Minor = rtdEvent.ServerVersion.Minor
                                        };
                                        _logger.LogInformation($"【SupOS时间同步】:服务器协议版本:【{MachineUtil.ServerVersion}】");
                                    }
                                    _logger.LogInformation($"【SupOS时间同步】:LocalTimeStamp:{rtdEvent.ServerTimeStamp},time:{Common.Extension.DateTime.ToTime(Convert.ToDouble(rtdEvent.ServerTimeStamp))}");
                                    await MachineUtil.SetTime(Common.Extension.DateTime.ToTime(Convert.ToDouble(rtdEvent.ServerTimeStamp)));
                                }
                                break;
                            case TransPondTopicPurposeEnum.CloudRequestRefreshRealTimeDeviceData:
                                {
                                    AddRecord("【SupOS】云端要求刷新设备实时数据", topic, level, "");
                                    _logger.LogInformation($"【SupOS】云端要求刷新设备实时数据 Topic:【{topic}】,Level:【{level}】");
                                    using var scopeService = _scopeFactory.CreateScope();
                                    var deviceRepo = scopeService.ServiceProvider.GetRequiredService<SqlSugarRepository<Device>>();
                                    var deviceList = await deviceRepo.AsQueryable().Where(w => w.Enable).Includes(w => w.DeviceVariable).ToListAsync();
                                    foreach (var device in deviceList)
                                        try
                                        {
                                            await MessageCenter.PublishAsync(string.Format(ConstMethod.CloudRequestRefreshRealTimeDeviceData, device.DeviceName), "");
                                        }
                                        catch (Exception exception)
                                        {
                                            AddRecord("【SupOS】云端要求刷新设备实时数据Error", topic, level, exception.Message);
                                        }
                                }
                                break;
                        }

                        break;
                    }
                case IoTPlatformType.RootCloud:
                    {
                        var payLoad = e.ApplicationMessage.ConvertPayloadToString();
                        _logger.LogInformation($"【RootCloud】 Topic:【{topic}】,Level:【{level}】 Payload:【{payLoad}】");
                        switch (getTopic.TransPondTopicPurpose)
                        {
                            case TransPondTopicPurposeEnum.Variable:
                                {
                                    //暂不支持
                                    break;
                                }
                            case TransPondTopicPurposeEnum.SysTimeSub:
                                {
                                    AddRecord("【RootCloud】时间同步", topic, level, payLoad);
                                    await _rootCloudApplication.ReceiveSysTime(payLoad);
                                    break;
                                }
                        }
                    }
                    break;
                case IoTPlatformType.MQTT:
                    {
                        var payLoad = e.ApplicationMessage.ConvertPayloadToString();
                        _logger.LogInformation($"【MQTT】 Topic:【{topic}】,Level:【{level}】 Payload:【{payLoad}】");
                        AddRecord("【MQTT】回调", topic, level, payLoad);

                        _engine.Engine.SetValue("payload", payLoad);
                        _engine.Engine.SetValue("time", Common.Extension.DateTime.ShangHai());

                        switch (getTopic.TransPondTopicPurpose)
                        {
                            case TransPondTopicPurposeEnum.SysTimeSub:
                                {
                                    var sysTimeSub = TransPond?.TransPondTopic.FirstOrDefault(f => f.TransPondTopicPurpose == TransPondTopicPurposeEnum.SysTimeSub);
                                    if (sysTimeSub == null)
                                        return;
                                    try
                                    {
                                        lock (this)
                                        {
                                            var getValue = _engine.Engine.Evaluate(sysTimeSub.Config ?? "", new ScriptParsingOptions { Tolerant = true, AllowReturnOutsideFunction = true }).ToObject();
                                            if (getValue != null)
                                                ShellUtil.Bash($"date -s \"{getValue.ToString():yyyy-MM-dd HH:mm:ss}\" && hwclock -w --systohc ").GetAwaiter().GetResult();
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        SendMessage($"【MQTT】 Topic:【{sysTimeSub.Topic}】,失败原因:【{ex.Message}】", "error", true);
                                    }

                                    break;
                                }
                            case TransPondTopicPurposeEnum.Variable:
                                {
                                    var variable = TransPond?.TransPondTopic.FirstOrDefault(f => f.TransPondTopicPurpose == TransPondTopicPurposeEnum.Variable);
                                    if (variable == null)
                                        return;
                                    try
                                    {
                                        lock (this)
                                        {
                                            var getValue = _engine.Engine.Evaluate(variable.Config ?? "", new ScriptParsingOptions { Tolerant = true, AllowReturnOutsideFunction = true }).ToObject();
                                            if (getValue != null)
                                            {
                                                var request = JSON.Deserialize<DeviceWriteRequest>(getValue.ToString());
                                                MessageCenter.PublishAsync(string.Format(ConstMethod.DeviceWrite, request.DeviceName), JSON.Serialize(request)).GetAwaiter().GetResult();
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        SendMessage($"【MQTT】 Topic:【{variable.Topic}】,失败原因:【{ex.Message}】", "error", true);
                                    }

                                    break;
                                }
                            case TransPondTopicPurposeEnum.Cmd:
                                {
                                    var variable = TransPond?.TransPondTopic.FirstOrDefault(f => f.TransPondTopicPurpose == TransPondTopicPurposeEnum.Cmd);
                                    if (variable == null)
                                        return;
                                    try
                                    {
                                        lock (this)
                                        {
                                            _engine.Engine.Evaluate(variable.Config ?? "", new ScriptParsingOptions { Tolerant = true, AllowReturnOutsideFunction = true }).ToObject();
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        SendMessage($"【MQTT】 Topic:【{variable.Topic}】,失败原因:【{ex.Message}】", "error", true);
                                    }

                                    break;
                                }
                        }

                        break;
                    }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"ClientId:{e.ClientId} Topic:{e.ApplicationMessage.Topic},Payload:{e.ApplicationMessage.ConvertPayloadToString()}," + ex);
        }
    }

    /// <summary>
    ///     通过topic 发送同步时间命令
    /// </summary>
    public async Task SyncSystemTime(object stat)
    {
        if (Client is { IsConnected: true })
        {
            if (TransPond == null)
                return;
            // 避免每次重启转发,重复同步时间
            if ((Common.Extension.DateTime.Now() - DeviceSendTime).TotalMinutes < 30)
                return;
            if (_noFirstTime)
                DeviceSendTime = Common.Extension.DateTime.Now();
            else
                _noFirstTime = true;
            // 时间同步topic
            var topic = TransPond.TransPondTopic.FirstOrDefault(f => f.TransPondTopicPurpose == TransPondTopicPurposeEnum.SysTime);
            if (topic == null)
                return;

            byte[] payload = new byte[] { };
            var formType = TransPond.MqttConfModel?.IoTPlatformType;
            switch (formType)
            {
                case IoTPlatformType.ELink:
                    {
                        payload = Encoding.UTF8.GetBytes(JSON.Serialize(new ELinkTimeReq { DeviceSendTime = Common.Extension.DateTime.TimeStamp() }));
                        break;
                    }
                case IoTPlatformType.SupOS:
                    {
                        payload = new RtdEvent
                        {
                            Topic = "SyncTime",
                            Payload = new GatewayCoordinate
                            {
                                LocalTimeStamp = Convert.ToInt64(Common.Extension.DateTime.ToTsStr(Common.Extension.DateTime.Now())),
                                LocalVersion = new ProtocolVersion
                                {
                                    Major = 1,
                                    Minor = 2,
                                }
                            }.ToByteString()
                        }.ToByteArray();
                        break;
                    }
                case IoTPlatformType.RootCloud:
                    {
                        // 发起时间同步请求
                        payload = Encoding.UTF8.GetBytes(JSON.Serialize(new TimeReq
                        {
                            Header = new HeaderReq { MsgId = Guid.NewGuid().ToString("N"), Ts = Common.Extension.DateTime.ToLong(Common.Extension.DateTime.Now()) }
                        }));
                        break;
                    }
            }

            var resp = await Client.PublishAsync(new MqttApplicationMessage
            {
                Topic = topic.Topic,
                PayloadSegment = payload,
                QualityOfServiceLevel = topic.Qos,
                Retain = false
            });

            if (resp.ReasonCode != MqttClientPublishReasonCode.Success)
                SendMessage($"Identifier:{TransPond.Identifier}, 发送时间同步请求Topic:{topic.Topic} 失败！,返回结果:{JSON.Serialize(resp)}", "error", true);
            else
                SendMessage($"Identifier:{TransPond.Identifier}, 发送时间同步请求Topic:{topic.Topic} 成功！", "");
        }
    }

    #region 私有方法

    #region 辅助

    /// <summary>检查mqtt是否正常连接</summary>
    protected virtual void MqttConnect()
    {
        if (Client == null || _tokenSource.IsCancellationRequested)
            return;

        if (Client.IsConnected)
            return;

        // 使用原子操作检查重连状态，避免多线程竞争
        if (Interlocked.CompareExchange(ref _reconnecting, 1, 0) == 0)
        {
            try
            {
                // 已获取重连标志，执行重连
                _logger.LogInformation("MQTT离线, 尝试重新连接...");

                // 启动异步重连任务，避免阻塞当前线程
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await SafeReconnect();
                    }
                    finally
                    {
                        // 释放重连标志
                        Interlocked.Exchange(ref _reconnecting, 0);
                    }
                });
            }
            catch (Exception ex)
            {
                // 发生异常时释放重连标志
                Interlocked.Exchange(ref _reconnecting, 0);
                _logger.LogError($"启动重连过程中发生错误: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 安全重连
    /// </summary>
    /// <returns></returns>
    private async Task SafeReconnect()
    {
        try
        {
            _logger.LogInformation("MQTT开始重新连接...");

            // 清理旧的 Client 实例
            if (Client != null)
            {
                // 移除事件处理器
                Client.ApplicationMessageReceivedAsync -= Client_ApplicationMessageReceived;
                Client.ConnectedAsync -= OnConnected;
                Client.DisconnectedAsync -= OnDisconnectedAsync;

                // 如果已连接则断开
                if (Client.IsConnected)
                {
                    await Client.DisconnectAsync();
                }

                // 释放资源
                Client.Dispose();
            }

            // 重新初始化客户端
            Client = new MqttFactory().CreateMqttClient();

            // 重新绑定事件处理
            Client.ApplicationMessageReceivedAsync += Client_ApplicationMessageReceived;
            Client.ConnectedAsync += OnConnected;
            Client.DisconnectedAsync += OnDisconnectedAsync;

            // 尝试重新连接
            await Client.ConnectAsync(_clientOptions, _tokenSource.Token);
        }
        catch (Exception ex)
        {
            _logger.LogError($"MQTT重连失败: {ex.Message}");
        }
    }

    #endregion

    /// <summary>
    ///     socket推送消息
    /// </summary>
    /// <param name="message"></param>
    /// <param name="leavl">消息等级</param>
    /// <param name="info">记录日志</param>
    private void SendMessage(string message, string leavl, bool info = false)
    {
        if (leavl == "error")
            message = $"<span style=\"color:#ff4d4f;\">{message}</span>";
        else if (leavl == "warning")
            message = $"<span style=\"color:#F1C40F;\">{message}</span>";
        else if (leavl == "debug")
            message = $"<span style=\"color:#1890ff;\">{message}</span>";
        _ = _send.DeviceResp(message, TransPond?.Identifier ?? "", info);
    }

    /// <summary>
    ///     添加数据上报记录
    /// </summary>
    /// <param name="offLine"></param>
    private Task ReportDataQueueAdd(OffLine offLine)
    {
        if (!saveData) return Task.CompletedTask;
        var readTime = Common.Extension.DateTime.ToTime(offLine.TimeStamp);
        ReportDataQueue.Add(new ReportData
        {
            Id = YitIdHelper.NextId(),
            Data = offLine.Params,
            DeviceName = offLine.DeviceName,
            ReadTime = readTime,
            ReportDataType = ReportDataTypeEnum.OffLine,
            Time = readTime.ToString("MM-dd-HH")
        });

        return Task.CompletedTask;
    }

    /// <summary>
    ///     ELink上报系统属性
    /// </summary>
    private async Task ELinkHeartbeat(object stat)
    {
        try
        {
            // 1. 检查必要条件
            if (MachineUtil.CurrentSystemEnvironment == null ||
                MachineUtil.CurrentSystemEnvironment == CurrentSystemEnvironmentEnum.Ubuntu ||
                Client?.IsConnected != true ||
                TransPond?.MqttConfModel == null ||
                MachineUtil.UseInfo == null)  // 添加UseInfo空检查
            {
                return;
            }

            var onLine = TransPond.TransPondTopic?.FirstOrDefault(f => f.TransPondTopicPurpose == TransPondTopicPurposeEnum.OnLine);
            if (onLine == null)
            {
                return;
            }

            var timeStamp = Common.Extension.DateTime.TimeStamp();
            var input = new VariableSendToPlatform
            {
                ParentTime = timeStamp,
                DeviceName = TransPond.MqttConfModel.ClientId ?? "unknown",
                Params = new Dictionary<string, TransPondParamValue>()
            };

            // 2. 安全地添加参数
            try
            {
                // 型号
                var readIdent = "fengEdge-200";
                if (File.Exists("/etc/DeviceConf/Ident.txt"))
                {
                    readIdent = (await File.ReadAllTextAsync("/etc/DeviceConf/Ident.txt"))?.Trim() ?? readIdent;
                }
                input.Params.Add("Model", new() { Value = readIdent, Time = timeStamp, DataType = TransPondDataTypeEnum.String });

                // 软件版本
                input.Params.Add("SoftwareVer", new()
                {
                    Value = MachineUtil.Version != null ? "V" + MachineUtil.Version : "unknown",
                    Time = timeStamp,
                    DataType = TransPondDataTypeEnum.String
                });

                // 网络信息 - 添加空检查
                if (MachineUtil.UseInfo.Network != null)
                {
                    input.Params.Add("Network", new()
                    {
                        Value = JSON.Serialize(MachineUtil.UseInfo.Network),
                        Time = timeStamp,
                        DataType = TransPondDataTypeEnum.String
                    });
                }

                // CPU使用率
                input.Params.Add("CpuRate", new()
                {
                    Value = MachineUtil.UseInfo.CpuRate.ToString(),
                    Time = timeStamp,
                    DataType = TransPondDataTypeEnum.Double
                });

                // 内存信息 - 添加空检查
                if (MachineUtil.UseInfo.MemInfo != null)
                {
                    input.Params.Add("MemTotal", new()
                    {
                        Value = MachineUtil.UseInfo.MemInfo.MemTotal.ToString(),
                        Time = timeStamp,
                        DataType = TransPondDataTypeEnum.Int
                    });
                    input.Params.Add("MemUsage", new()
                    {
                        Value = MachineUtil.UseInfo.MemInfo.MemUsage.ToString(),
                        Time = timeStamp,
                        DataType = TransPondDataTypeEnum.Int
                    });
                    input.Params.Add("MemAvailable", new()
                    {
                        Value = MachineUtil.UseInfo.MemInfo.MemAvailable.ToString(),
                        Time = timeStamp,
                        DataType = TransPondDataTypeEnum.Int
                    });
                    input.Params.Add("MemRate", new()
                    {
                        Value = MachineUtil.UseInfo.MemInfo.MemRate.ToString(),
                        Time = timeStamp,
                        DataType = TransPondDataTypeEnum.Double
                    });
                }

                // 磁盘信息 - 添加空检查
                if (MachineUtil.UseInfo.DiskInfo != null)
                {
                    input.Params.Add("DiskSize", new()
                    {
                        Value = MachineUtil.UseInfo.DiskInfo.DiskSize.ToString(),
                        Time = timeStamp,
                        DataType = TransPondDataTypeEnum.Int
                    });
                    input.Params.Add("DiskUsed", new()
                    {
                        Value = MachineUtil.UseInfo.DiskInfo.DiskUsed.ToString(),
                        Time = timeStamp,
                        DataType = TransPondDataTypeEnum.Int
                    });
                    input.Params.Add("DiskAvailable", new()
                    {
                        Value = MachineUtil.UseInfo.DiskInfo.DiskAvailable.ToString(),
                        Time = timeStamp,
                        DataType = TransPondDataTypeEnum.Int
                    });
                    input.Params.Add("DiskRate", new()
                    {
                        Value = MachineUtil.UseInfo.DiskInfo.DiskRate.ToString(),
                        Time = timeStamp,
                        DataType = TransPondDataTypeEnum.Double
                    });
                }

                // 3. 发送数据
                var onLineSuccess = await _eLinkApplication.PublishAsync(JSON.Serialize(input), onLine, Client);
                if (onLineSuccess?.ReasonCode == MqttClientPublishReasonCode.Success)
                {
                    SendMessage($"【网关状态】 MQTT推送实时数据成功,Topic:【{onLine.Topic}】", "debug");
                }
                else
                {
                    SendMessage($"【网关状态】 MQTT推送实时数据失败,Topic:【{onLine.Topic}】,返回结果:{JSON.Serialize(onLineSuccess)}", "error", true);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"构建网关状态数据时发生错误: {ex.Message}");
                throw;
            }
        }
        catch (Exception ex)
        {
            SendMessage($"【网关状态】 网关属性上报 Error:{ex.Message}", "error", true);
            _logger?.LogError($"网关状态上报失败: {ex.Message}\n{ex.StackTrace}");
        }
    }

    /// <summary>
    ///     发送成功数据保存到数据库中
    /// </summary>
    private async Task AddReportData(Object state)
    {
        try
        {
            var list = ReportDataQueue.GetQueue()?.ToList();
            ReportDataQueue.Complete();
            if (list == null || list.Count == 0)
                return;
            else
            {
                await MessageCenter.PublishAsync(EventConst.PubSuccess, list.ToJson());
                list.Clear();
            }

            list = ReportDataQueue.GetQueue()?.ToList();
            if (list != null && list.Count != 0)
            {
                _timerAddReportData.SetNext(0);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"【发送成功数据事件】 Error:{ex.Message}");
        }
    }

    /// <summary>
    ///     实时数据保存失败的数据
    /// </summary>
    private async Task OffLineQueueTask(Object state)
    {
        try
        {
            var list = OffLineQueue.GetQueue()?.ToList();
            OffLineQueue.Complete();
            if (list == null || list.Count == 0)
                return;
            else
                try
                {
                    using var scopeService = _scopeFactory.CreateScope();
                    // 切换数据库，写入数据
                    var offLineDbRep = scopeService.ServiceProvider.GetRequiredService<ISqlSugarClient>();
                    await offLineDbRep.AsTenant().GetConnection(SqlSugarConst.EdgeData).CopyNew().Insertable(list).ExecuteCommandAsync();
                    list.Clear();
                    offLineDbRep.Dispose();
                    // GC.Collect();
                }
                catch (Exception ex)
                {
                    // 忽略该错误
                    _logger.LogError($"【实时数据保存数据】 Error:{ex.Message}");
                    foreach (var offLine in list)
                        OffLineQueue.Add(offLine);
                }

            list = OffLineQueue.GetQueue()?.ToList();
            if (list != null && list.Count != 0)
                _timerOffLineQueueTask.SetNext(0);
        }
        catch (Exception ex)
        {
            // 这里不抛出异常，避免中断写入
            _logger.LogError($"【实时数据保存数据】 异常:{ex.Message}");
        }
    }

    /// <summary>
    ///     添加连接事件
    /// </summary>
    /// <param name="message"></param>
    private void AddToConnectRecord(string message)
    {
        if (Timelines.Count > 200)
            Timelines.Dequeue();

        Timelines.Enqueue(new RunRecordLine
        {
            Time = Common.Extension.DateTime.ShangHai(),
            Message = message
        });
    }

    /// <summary>
    ///     添加MQTT日志记录
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="topic">topic</param>
    /// <param name="level">qps</param>
    /// <param name="payLoad">内容</param>
    private void AddRecord(string title, string topic, MqttQualityOfServiceLevel level, string payLoad)
    {
        if (RecordLines.Count > 300)
            RecordLines.Dequeue();

        var tmp = TP.Wrapper(title,
            $"##时间## 【{Common.Extension.DateTime.NowString()}】",
            $"##Topic## 【{topic}】",
            $"##Level## 【{level}】",
            $"##PayLoad## 【{payLoad}】");
        RecordLines.Enqueue(new RunRecordLine
        {
            Time = Common.Extension.DateTime.ShangHai(),
            Message = tmp
        });
    }

    /// <summary>
    /// 检查订阅状态
    /// </summary>
    private async Task CheckSubscriptionStatus(object state)
    {
        try
        {
            if (!Client?.IsConnected ?? true)
            {
                _logger?.LogInformation("MQTT未连接，跳过订阅检查");
                return;
            }

            if (_mqttSubscribeOptions == null)
            {
                return;
            }

            // 通过获取mqtt已经订阅的数量进行判断
            if (!_isSubscribed)
            {
                _logger?.LogInformation("检测到MQTT主题未完全订阅，尝试重新订阅...");
                await _subscriptionLock.WaitAsync();
                try
                {
                    // 重新订阅
                    await EnsureSubscription();
                }
                finally
                {
                    _subscriptionLock.Release();
                }
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError($"检查MQTT订阅状态时发生错误: {ex.Message}");
        }
    }

    #endregion

    /// <summary>
    ///     停止master节点连接
    /// </summary>
    public void StopMaster()
    {
        if (Client == null)
            return;
        // 重置状态
        _isSubscribed = false;
        //连接断开
        Client.DisconnectedAsync -= OnDisconnectedAsync;
        //消息回调
        Client.ApplicationMessageReceivedAsync -= Client_ApplicationMessageReceived;
        //连接成功
        Client.ConnectedAsync -= OnConnected;
        if (Client is { IsConnected: true })
            Client.DisconnectAsync();
        Client.Dispose();
        _timerPushOffLineData?.Dispose();
        _timerAddReportData?.Dispose();
        _timerOffLineQueueTask?.Dispose();
        _syncSystemTime?.Dispose();
        _commonTimer?.Dispose();
        _checkSubscriptionTimer?.Dispose();
        _tokenSource.Cancel();
    }

    /// <summary>
    /// </summary>
    public void Dispose()
    {
        _tokenSource.Cancel();
        Client?.Dispose();
        ReportDataQueue.Dispose();
        OffLineQueue.Dispose();
    }

    // 添加重连状态标志
    private int _reconnecting = 0;
}