<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>Common</RootNamespace>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Furion.Pure" Version="4.9.5.26" />
        <PackageReference Include="MQTTnet" Version="4.3.6.1152"/>
        <PackageReference Include="OPCFoundation.NetStandard.Opc.Ua" Version="1.5.376.244" />
        <PackageReference Include="YamlDotNet" Version="16.0.0"/>
    </ItemGroup>

</Project>
